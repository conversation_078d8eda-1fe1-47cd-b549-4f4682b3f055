/*
 * @Description: 
 * @Author: your name
 * @version: 
 * @Date: 2024-06-24 09:20:01
 * @LastEditors: your name
 * @LastEditTime: 2024-12-09 10:03:32
 */
import api from "./request";
// 获取楼栋信息
export const getBuild = (params) => api.get(`/getBuild`, params)
// 添加楼栋数据
export const addHouse = (params) => api.post(`/addHouse`, params)
// 更新房户信息
export const updateInfo = (params) => api.post(`/updateInfo`, params)
// 通过分页的方式获取房户信息
export const getHouse = (params) => api.get(`/getHouse`, { params })
// 获取住户详细信息
export const getOneHouseInfo = (params) => api.get(`/getOneHouseInfo`, { params })
