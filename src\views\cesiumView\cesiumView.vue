<template>
  <drawTools></drawTools>
  <div style="position: absolute; z-index: 100" class="router-wrapper">
    <router-view></router-view>
  </div>
  <keep-alive>
    <cesiumEarthLand>
    </cesiumEarthLand>
  </keep-alive>
</template>
<script setup>
import router from '../../router'
import { h, ref } from 'vue';
import cesiumEarthLand from './components/cesiumEarthLand.vue'
import drawTools from './components/drawTools.vue'

</script>

<style scoped>
.router-wrapper {
  width: 100%;
  /* 注意calc计算符号要保留空格 */
  height: calc(100% - 20px);
  pointer-events: none;
  padding: 20px;
}
.ant-menu {
  position: relative;
  z-index: 10000;
}

</style>