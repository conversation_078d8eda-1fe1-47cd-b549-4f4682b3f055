<template>
  <div class="draw-tools-container">
    <el-dropdown trigger="click" placement="bottom-start">
      <el-button type="primary" class="tools-button">
        绘制工具
        <el-icon class="el-icon--right">
          <arrow-down />
        </el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="drawLine">
            <el-icon><edit /></el-icon>
            绘制线
          </el-dropdown-item>
          <el-dropdown-item @click="drawPoint">
            <el-icon><location /></el-icon>
            绘制点
          </el-dropdown-item>
          <el-dropdown-item @click="drawCircle">
            <el-icon><circle-plus /></el-icon>
            绘制圆形
          </el-dropdown-item>
          <!-- <el-dropdown-item @click="drawRectangle">
            <el-icon><grid /></el-icon>
            绘制矩形
          </el-dropdown-item> -->
          <el-dropdown-item @click="drawPolygon">
            <el-icon><crop /></el-icon>
            绘制多边形
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 鼠标提示框 -->
    <div
      v-if="showTooltip"
      class="draw-tooltip"
      :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }"
    >
      {{ tooltipText }}
    </div>
  </div>
</template>

<script setup>
import {
  ArrowDown,
  Edit,
  Location,
  CirclePlus,
  Grid,
  Crop,
} from '@element-plus/icons-vue'
import DrawTool from '../../../cesiumTools/drawTool.js'
import { useMapData } from '../../../store'
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const mapStore = useMapData()
const drawTool = ref(null)

// 提示框相关状态
const showTooltip = ref(false)
const tooltipPosition = ref({ x: 0, y: 0 })
const tooltipText = ref('')
const isDrawing = ref(false)
const currentDrawType = ref('')

// 监听 viewer 的变化，当 viewer 可用时创建 DrawTool 实例
watch(
  () => mapStore.Viewer,
  (newViewer) => {
    if (newViewer && !drawTool.value) {
      drawTool.value = new DrawTool(newViewer)
      setupDrawEvents()
    }
  },
  { immediate: true }
)

// 设置绘制事件监听
const setupDrawEvents = () => {
  if (!mapStore.Viewer) return

  const viewer = mapStore.Viewer

  // 监听鼠标移动事件
  viewer.canvas.addEventListener('mousemove', handleMouseMove)

  // 监听鼠标点击事件
  viewer.canvas.addEventListener('click', handleMouseClick)

  // 监听右键点击事件
  viewer.canvas.addEventListener('contextmenu', handleRightClick)
}

// 处理鼠标移动
const handleMouseMove = (event) => {
  if (showTooltip.value) {
    tooltipPosition.value = {
      x: event.clientX - 30, // 从鼠标箭头尖端开始，稍微偏移
      y: event.clientY - 55, // 从鼠标箭头尖端开始，稍微偏移
    }
  }
}

// 处理鼠标左键点击
const handleMouseClick = (event) => {
  if (showTooltip.value && !isDrawing.value) {
    isDrawing.value = true
    updateTooltipText()
  }
}

// 处理鼠标右键点击
const handleRightClick = (event) => {
  event.preventDefault()
  if (showTooltip.value && isDrawing.value) {
    // 结束绘制
    endDrawing()
  }
}

// 更新提示文本
const updateTooltipText = () => {
  if (!isDrawing.value) {
    switch (currentDrawType.value) {
      case 'Polyline':
        tooltipText.value = '单击鼠标左键开始绘制线条'
        break
      case 'Point':
        tooltipText.value = '单击鼠标左键放置点'
        break
      case 'Circle':
        tooltipText.value = '单击鼠标左键开始绘制圆形'
        break
      case 'Rect':
        tooltipText.value = '单击鼠标左键开始绘制矩形'
        break
      case 'Polygon':
        tooltipText.value = '单击鼠标左键开始绘制多边形'
        break
      default:
        tooltipText.value = '单击鼠标左键开始绘制'
    }
  } else {
    switch (currentDrawType.value) {
      case 'Polyline':
        tooltipText.value = '继续点击添加点，右键结束绘制'
        break
      case 'Point':
        tooltipText.value = '点已放置'
        setTimeout(() => endDrawing(), 500) // 点绘制完成后自动结束
        break
      case 'Circle':
        tooltipText.value = '移动鼠标调整半径，右键结束绘制'
        break
      case 'Rect':
        tooltipText.value = '移动鼠标调整大小，右键结束绘制'
        break
      case 'Polygon':
        tooltipText.value = '继续点击添加点，右键结束绘制'
        break
      default:
        tooltipText.value = '右键结束绘制'
    }
  }
}

// 开始绘制
const startDrawing = (type) => {
  currentDrawType.value = type
  isDrawing.value = false
  showTooltip.value = true
  updateTooltipText()

  if (drawTool.value) {
    drawTool.value.active(type)
  } else {
    console.warn('DrawTool 还未初始化')
  }
}

// 结束绘制
const endDrawing = () => {
  showTooltip.value = false
  isDrawing.value = false
  currentDrawType.value = ''

  // 这里可以调用drawTool的停止方法，如果有的话
  // if (drawTool.value && drawTool.value.deactivate) {
  //   drawTool.value.deactivate()
  // }
}

// 绘制功能方法
const drawLine = () => {
  startDrawing('Polyline')
}

const drawPoint = () => {
  startDrawing('Point')
}

const drawCircle = () => {
  startDrawing('Circle')
}

const drawRectangle = () => {
  startDrawing('Rect')
}

const drawPolygon = () => {
  startDrawing('Polygon')
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (mapStore.Viewer) {
    const viewer = mapStore.Viewer
    viewer.canvas.removeEventListener('mousemove', handleMouseMove)
    viewer.canvas.removeEventListener('click', handleMouseClick)
    viewer.canvas.removeEventListener('contextmenu', handleRightClick)
  }
})
</script>

<style scoped>
.draw-tools-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.tools-button {
  min-width: 120px;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 鼠标提示框样式 */
.draw-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 10000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transform: translateZ(0); /* 硬件加速 */
}

.draw-tooltip::before {
  content: '';
  position: absolute;
  top: 100%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}
</style>
