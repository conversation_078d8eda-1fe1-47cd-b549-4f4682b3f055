<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="32px"
   height="32px"
   id="svg3797"
   version="1.1"
   inkscape:version="0.48.5 r10040"
   sodipodi:docname="focus_2.svg"
   inkscape:export-filename="D:\dev\workspaces\potree\develop\resources\icons\focus.png"
   inkscape:export-xdpi="90"
   inkscape:export-ydpi="90">
  <defs
     id="defs3799">
    <linearGradient
       id="linearGradient3789">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop3791" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop3793" />
    </linearGradient>
    <marker
       inkscape:stockid="Arrow1Lstart"
       orient="auto"
       refY="0.0"
       refX="0.0"
       id="Arrow1Lstart"
       style="overflow:visible">
      <path
         id="path3832"
         d="M 0.0,0.0 L 5.0,-5.0 L -12.5,0.0 L 5.0,5.0 L 0.0,0.0 z "
         style="fill-rule:evenodd;stroke:#000000;stroke-width:1.0pt"
         transform="scale(0.8) translate(12.5,0)" />
    </marker>
    <marker
       inkscape:stockid="DotL"
       orient="auto"
       refY="0.0"
       refX="0.0"
       id="DotL"
       style="overflow:visible">
      <path
         id="path3893"
         d="M -2.5,-1.0 C -2.5,1.7600000 -4.7400000,4.0 -7.5,4.0 C -10.260000,4.0 -12.5,1.7600000 -12.5,-1.0 C -12.5,-3.7600000 -10.260000,-6.0 -7.5,-6.0 C -4.7400000,-6.0 -2.5,-3.7600000 -2.5,-1.0 z "
         style="fill-rule:evenodd;stroke:#000000;stroke-width:1.0pt"
         transform="scale(0.8) translate(7.4, 1)" />
    </marker>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3789"
       id="radialGradient3797"
       cx="19.276922"
       cy="8.2230759"
       fx="19.276922"
       fy="8.2230759"
       r="14"
       gradientTransform="matrix(1.3,0,0,1.2535715,-5.4599991,-2.2082142)"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="22.395604"
     inkscape:cx="4.3873333"
     inkscape:cy="18.394162"
     inkscape:current-layer="layer1"
     showgrid="true"
     inkscape:grid-bbox="true"
     inkscape:document-units="px"
     inkscape:window-width="1920"
     inkscape:window-height="1138"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1">
    <inkscape:grid
       type="xygrid"
       id="grid3805" />
  </sodipodi:namedview>
  <metadata
     id="metadata3802">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     inkscape:label="Layer 1"
     inkscape:groupmode="layer">
    <path
       sodipodi:type="arc"
       style="fill-opacity:1.0;stroke:none;stroke-width:14;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0.40000000000000002;opacity:1;fill:url(#radialGradient3797)"
       id="path2989"
       sodipodi:cx="14"
       sodipodi:cy="13.5"
       sodipodi:rx="14"
       sodipodi:ry="13.5"
       d="m 28,13.5 a 14,13.5 0 1 1 -28,0 14,13.5 0 1 1 28,0 z"
       transform="matrix(0.71428571,0,0,0.74074075,6.0000003,6)" />
  </g>
</svg>
