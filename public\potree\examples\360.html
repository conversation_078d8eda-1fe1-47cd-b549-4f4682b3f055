<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../../libs/spectrum/spectrum.js"></script>
	<script src="../../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../../libs/other/BinaryHeap.js"></script>
	<script src="../../libs/tween/tween.min.js"></script>
	<script src="../../libs/d3/d3.js"></script>
	<script src="../../libs/proj4/proj4.js"></script>
	<script src="../../libs/openlayers3/ol.js"></script>
	<script src="../../libs/i18next/i18next.js"></script>
	<script src="../../libs/jstree/jstree.js"></script>
	<script src="../../build/potree/potree.js"></script>
	<script src="../../libs/plasio/js/laslaz.js"></script>
	
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../../build/potree/resources/images/background.jpg');">
		
		</div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">

	import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(2_000_000);
		viewer.loadSettingsFromURL();
		viewer.setDescription(`Mobile LIDAR with 360 degree image overlays. 
		Click on a sphere to enter 360 view. Click "unfocus" to leave 360 view. <br>
		Point cloud courtesy of <a href="http://www.helimap.com/">Helimap System SA</a>. Images were downsized for this online demo; Original size is 8000x400.`);
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			viewer.toggleSidebar();
		});
		
		// Load and add point cloud to scene
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/helimap/360/MLS_drive1/cloud.js", "MLS", e => {
			let scene = viewer.scene;
			let pointcloud = e.pointcloud;
			
			let material = pointcloud.material;
			material.size = 0.5;
			material.minSize = 2.0;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.shape = Potree.PointShape.SQUARE;
			material.activeAttributeName = "rgba";
			
			scene.addPointCloud(pointcloud);

			viewer.scene.view.setView(
				[2652381.103, 1249049.447, 411.636],
				[2652364.407, 1249077.205, 399.696],
			);

			run();
		});

		async function run(){

			proj4.defs("WGS84", "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs");
			proj4.defs("pointcloud", viewer.getProjection());
			let transform = proj4("WGS84", "pointcloud");

			let params = {
				transform: transform
			};

			// this file contains coordinates, orientation and filenames of the images:
			// http://**********/mschuetz/potree/resources/pointclouds/helimap/360/Drive2_selection/coordinates.txt
			Potree.Images360Loader.load("http://**********/mschuetz/potree/resources/pointclouds/helimap/360/Drive2_selection", viewer, params).then( images => {
				viewer.scene.add360Images(images);
			});

			viewer.mapView.showSources(false);
		}


		
	</script>
	
	
  </body>
</html>
