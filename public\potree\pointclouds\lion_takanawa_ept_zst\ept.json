{"bounds": [-7.0, -1.0, -6.0, 3.0, 9.0, 4.0], "boundsConforming": [-5.0, 1.0, -4.0, 1.0, 7.0, 3.0], "dataType": "zstandard", "hierarchyType": "json", "points": 341989, "schema": [{"name": "X", "offset": -2.0, "scale": 0.001, "size": 4, "type": "signed"}, {"name": "Y", "offset": 4.0, "scale": 0.001, "size": 4, "type": "signed"}, {"name": "Z", "offset": -1.0, "scale": 0.001, "size": 4, "type": "signed"}, {"name": "Intensity", "size": 2, "type": "unsigned"}, {"name": "ReturnNumber", "size": 1, "type": "unsigned"}, {"name": "NumberOfReturns", "size": 1, "type": "unsigned"}, {"name": "ScanDirectionFlag", "size": 1, "type": "unsigned"}, {"name": "EdgeOfFlightLine", "size": 1, "type": "unsigned"}, {"name": "Classification", "size": 1, "type": "unsigned"}, {"name": "ScanAngleRank", "size": 4, "type": "float"}, {"name": "UserData", "size": 1, "type": "unsigned"}, {"name": "PointSourceId", "size": 2, "type": "unsigned"}, {"name": "Red", "size": 2, "type": "unsigned"}, {"name": "Green", "size": 2, "type": "unsigned"}, {"name": "Blue", "size": 2, "type": "unsigned"}, {"name": "OriginId", "size": 4, "type": "unsigned"}], "span": 256, "srs": {}, "version": "1.0.0"}