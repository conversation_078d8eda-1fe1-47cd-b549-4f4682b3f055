<template>
  <div>
    <div class="button-container">
      <button
        @click="switchToPage('threeNavigate')"
        :class="{ active: activeComponent === 'threeNavigate' }"
      >
        VR
      </button>
      <button
        @click="switchToPage('floorPlan')"
        :class="{ active: activeComponent === 'floorPlan' }"
      >
        户型图
      </button>
      <button
        @click="switchToPage('three')"
        :class="{ active: activeComponent === 'three' }"
        :disabled="isLoading"
      >
        三维模型
      </button>
      <button
        @click="switchToPage('cesium')"
        :class="{ active: activeComponent === 'cesium' }"
      >
        Cesium
      </button>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p>{{ loadingMessage }}</p>
      </div>
    </div>

    <!-- 毛玻璃效果的动态组件转场 -->
    <transition name="glass-blur" mode="out-in">
      <component
        :is="currentComponent"
        :key="activeComponent"
        v-if="!isLoading"
      />
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import threeNavigate from './views/three-D-navigating.vue'
import ThreeView from './views/three.vue'
import FloorPlan from './views/floorPlan.vue'
import CesiumView from './views/cesiumView/cesiumView.vue'
// 当前激活的组件名
const activeComponent = ref('threeNavigate')
// 加载状态
const isLoading = ref(false)
const loadingMessage = ref('')

// 映射组件名到组件
const viewsMap = {
  threeNavigate: threeNavigate,
  three: ThreeView,
  floorPlan: FloorPlan,
  cesium: CesiumView,
}

// 获取当前应渲染的组件
const currentComponent = computed(() => viewsMap[activeComponent.value])

// 优化的页面切换函数
const switchToPage = async (pageName) => {
  if (pageName === 'three') {
    isLoading.value = true
    loadingMessage.value = '正在加载三维模型...'

    setTimeout(() => {
      activeComponent.value = pageName
      isLoading.value = false
    }, 1000)
  } else {
    activeComponent.value = pageName
  }
}

// 处理从户型图切换到VR的事件
let switchToVRHandler
let switchToFloorPlanHandler

onMounted(() => {
  switchToVRHandler = (event) => {
    const roomName = event.detail.room
    console.log(`从户型图切换到VR浏览: ${roomName}`)
    activeComponent.value = 'threeNavigate'
  }

  switchToFloorPlanHandler = (event) => {
    console.log('从VR返回户型图')
    activeComponent.value = 'floorPlan'
  }

  window.addEventListener('switchToVR', switchToVRHandler)
  window.addEventListener('switchToFloorPlan', switchToFloorPlanHandler)
})

onUnmounted(() => {
  if (switchToVRHandler) {
    window.removeEventListener('switchToVR', switchToVRHandler)
  }
  if (switchToFloorPlanHandler) {
    window.removeEventListener('switchToFloorPlan', switchToFloorPlanHandler)
  }
})
</script>

<style scoped>
.button-container {
  position: fixed;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  gap: 40px;
}

.button-container button {
  padding: 0;
  font-size: 16px;
  border: none;
  background-color: transparent;
  color: white;
  cursor: pointer;
  transition: border-bottom 0.3s ease;
  text-align: center;
  font-family: 'Microsoft Yahei', sans-serif;
}

.button-container button.active {
  border-bottom: 2px solid white;
}

.button-container button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 毛玻璃转场动画 */
.glass-blur-enter-active,
.glass-blur-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-blur-enter-from {
  opacity: 0;
  filter: blur(20px) brightness(1.2);
  transform: scale(1.05);
  backdrop-filter: blur(10px);
}

.glass-blur-leave-to {
  opacity: 0;
  filter: blur(15px) brightness(0.8);
  transform: scale(0.95);
  backdrop-filter: blur(8px);
}

.glass-blur-enter-to,
.glass-blur-leave-from {
  opacity: 1;
  filter: blur(0px) brightness(1);
  transform: scale(1);
  backdrop-filter: blur(0px);
}

/* 加载覆盖层 - 也使用毛玻璃效果 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeInGlass 0.3s ease-out;
}

@keyframes fadeInGlass {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

.loading-content {
  text-align: center;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
