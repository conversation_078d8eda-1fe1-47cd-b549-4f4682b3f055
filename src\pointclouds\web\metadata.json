{"version": "2.0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "points": 4268187, "projection": "", "hierarchy": {"firstChunkSize": 4444, "stepSize": 4, "depth": 6}, "offset": [-14.644, -20.298000000000002, -1.5230000000000001], "scale": [0.001, 0.001, 0.001], "spacing": 0.21851562500000002, "boundingBox": {"min": [-14.644, -20.298000000000002, -1.5230000000000001], "max": [13.326000000000002, 7.672000000000001, 26.447000000000003]}, "encoding": "DEFAULT", "attributes": [{"name": "position", "description": "", "size": 12, "numElements": 3, "elementSize": 4, "type": "int32", "min": [-14.644, -20.298000000000002, -1.5230000000000001], "max": [9.370000000000001, 7.672000000000001, 2.1], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "intensity", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [255], "scale": [1], "offset": [0]}, {"name": "return number", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "number of returns", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "classification", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "histogram": [4268187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "scan angle rank", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "user data", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "point source id", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "gps-time", "description": "", "size": 8, "numElements": 1, "elementSize": 8, "type": "double", "min": [205393.844], "max": [205665.199], "scale": [1], "offset": [0]}, {"name": "rgb", "description": "", "size": 6, "numElements": 3, "elementSize": 2, "type": "uint16", "min": [0, 0, 0], "max": [65535, 65535, 65535], "scale": [1, 1, 1], "offset": [0, 0, 0]}]}