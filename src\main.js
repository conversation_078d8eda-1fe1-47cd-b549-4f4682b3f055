import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

// 引入 Ant Design Vue
import Antd from 'ant-design-vue'

// 引入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// Photo Sphere Viewer 核心样式
import '@photo-sphere-viewer/core/index.css'

// 插件样式（必须一一对应）
import '@photo-sphere-viewer/virtual-tour-plugin/index.css'
import '@photo-sphere-viewer/gallery-plugin/index.css'
import '@photo-sphere-viewer/markers-plugin/index.css'

const app = createApp(App)
const pinia = createPinia()

// 注册 Ant Design Vue 和 Element Plus
app.use(ElementPlus)
app.use(pinia)
app.mount('#app')
