/**
 * Modules in this bundle
 * @license
 *
 * opentype.js:
 *   license: MIT (http://opensource.org/licenses/MIT)
 *   author: <PERSON><PERSON><PERSON> <<EMAIL>>
 *   version: 0.6.5
 *
 * tiny-inflate:
 *   license: MIT (http://opensource.org/licenses/MIT)
 *   author: <PERSON> <<EMAIL>>
 *   maintainers: devongo<PERSON>t <<EMAIL>>
 *   homepage: https://github.com/devongovett/tiny-inflate
 *   version: 1.0.2
 *
 * This header is generated by licensify (https://github.com/twada/licensify)
 */
(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.opentype = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
function Tree(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}function Data(e,t){this.source=e,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=t,this.destLen=0,this.ltree=new Tree,this.dtree=new Tree}function tinf_build_bits_base(e,t,n,r){var s,i;for(s=0;n>s;++s)e[s]=0;for(s=0;30-n>s;++s)e[s+n]=s/n|0;for(i=r,s=0;30>s;++s)t[s]=i,i+=1<<e[s]}function tinf_build_fixed_trees(e,t){var n;for(n=0;7>n;++n)e.table[n]=0;for(e.table[7]=24,e.table[8]=152,e.table[9]=112,n=0;24>n;++n)e.trans[n]=256+n;for(n=0;144>n;++n)e.trans[24+n]=n;for(n=0;8>n;++n)e.trans[168+n]=280+n;for(n=0;112>n;++n)e.trans[176+n]=144+n;for(n=0;5>n;++n)t.table[n]=0;for(t.table[5]=32,n=0;32>n;++n)t.trans[n]=n}function tinf_build_tree(e,t,n,r){var s,i;for(s=0;16>s;++s)e.table[s]=0;for(s=0;r>s;++s)e.table[t[n+s]]++;for(e.table[0]=0,i=0,s=0;16>s;++s)offs[s]=i,i+=e.table[s];for(s=0;r>s;++s)t[n+s]&&(e.trans[offs[t[n+s]]++]=s)}function tinf_getbit(e){e.bitcount--||(e.tag=e.source[e.sourceIndex++],e.bitcount=7);var t=1&e.tag;return e.tag>>>=1,t}function tinf_read_bits(e,t,n){if(!t)return n;for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var r=e.tag&65535>>>16-t;return e.tag>>>=t,e.bitcount-=t,r+n}function tinf_decode_symbol(e,t){for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var n=0,r=0,s=0,i=e.tag;do r=2*r+(1&i),i>>>=1,++s,n+=t.table[s],r-=t.table[s];while(r>=0);return e.tag=i,e.bitcount-=s,t.trans[n+r]}function tinf_decode_trees(e,t,n){var r,s,i,a,o,_;for(r=tinf_read_bits(e,5,257),s=tinf_read_bits(e,5,1),i=tinf_read_bits(e,4,4),a=0;19>a;++a)lengths[a]=0;for(a=0;i>a;++a){var f=tinf_read_bits(e,3,0);lengths[clcidx[a]]=f}for(tinf_build_tree(code_tree,lengths,0,19),o=0;r+s>o;){var d=tinf_decode_symbol(e,code_tree);switch(d){case 16:var b=lengths[o-1];for(_=tinf_read_bits(e,2,3);_;--_)lengths[o++]=b;break;case 17:for(_=tinf_read_bits(e,3,3);_;--_)lengths[o++]=0;break;case 18:for(_=tinf_read_bits(e,7,11);_;--_)lengths[o++]=0;break;default:lengths[o++]=d}}tinf_build_tree(t,lengths,0,r),tinf_build_tree(n,lengths,r,s)}function tinf_inflate_block_data(e,t,n){for(;;){var r=tinf_decode_symbol(e,t);if(256===r)return TINF_OK;if(256>r)e.dest[e.destLen++]=r;else{var s,i,a,o;for(r-=257,s=tinf_read_bits(e,length_bits[r],length_base[r]),i=tinf_decode_symbol(e,n),a=e.destLen-tinf_read_bits(e,dist_bits[i],dist_base[i]),o=a;a+s>o;++o)e.dest[e.destLen++]=e.dest[o]}}}function tinf_inflate_uncompressed_block(e){for(var t,n,r;e.bitcount>8;)e.sourceIndex--,e.bitcount-=8;if(t=e.source[e.sourceIndex+1],t=256*t+e.source[e.sourceIndex],n=e.source[e.sourceIndex+3],n=256*n+e.source[e.sourceIndex+2],t!==(65535&~n))return TINF_DATA_ERROR;for(e.sourceIndex+=4,r=t;r;--r)e.dest[e.destLen++]=e.source[e.sourceIndex++];return e.bitcount=0,TINF_OK}function tinf_uncompress(e,t){var n,r,s,i=new Data(e,t);do{switch(n=tinf_getbit(i),r=tinf_read_bits(i,2,0)){case 0:s=tinf_inflate_uncompressed_block(i);break;case 1:s=tinf_inflate_block_data(i,sltree,sdtree);break;case 2:tinf_decode_trees(i,i.ltree,i.dtree),s=tinf_inflate_block_data(i,i.ltree,i.dtree);break;default:s=TINF_DATA_ERROR}if(s!==TINF_OK)throw new Error("Data error")}while(!n);return i.destLen<i.dest.length?"function"==typeof i.dest.slice?i.dest.slice(0,i.destLen):i.dest.subarray(0,i.destLen):i.dest}var TINF_OK=0,TINF_DATA_ERROR=-3,sltree=new Tree,sdtree=new Tree,length_bits=new Uint8Array(30),length_base=new Uint16Array(30),dist_bits=new Uint8Array(30),dist_base=new Uint16Array(30),clcidx=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),code_tree=new Tree,lengths=new Uint8Array(320),offs=new Uint16Array(16);tinf_build_fixed_trees(sltree,sdtree),tinf_build_bits_base(length_bits,length_base,4,3),tinf_build_bits_base(dist_bits,dist_base,2,1),length_bits[28]=0,length_base[28]=258,module.exports=tinf_uncompress;

},{}],2:[function(require,module,exports){
"use strict";exports.fail=function(r){throw new Error(r)},exports.argument=function(r,t){r||exports.fail(t)},exports.assert=exports.argument;

},{}],3:[function(require,module,exports){
"use strict";function line(e,i,n,o,t){e.beginPath(),e.moveTo(i,n),e.lineTo(o,t),e.stroke()}exports.line=line;

},{}],4:[function(require,module,exports){
"use strict";function DefaultEncoding(e){this.font=e}function CmapEncoding(e){this.cmap=e}function CffEncoding(e,l){this.encoding=e,this.charset=l}function GlyphNames(e){var l;switch(e.version){case 1:this.names=exports.standardNames.slice();break;case 2:for(this.names=new Array(e.numberOfGlyphs),l=0;l<e.numberOfGlyphs;l++)this.names[l]=e.glyphNameIndex[l]<exports.standardNames.length?exports.standardNames[e.glyphNameIndex[l]]:e.names[e.glyphNameIndex[l]-exports.standardNames.length];break;case 2.5:for(this.names=new Array(e.numberOfGlyphs),l=0;l<e.numberOfGlyphs;l++)this.names[l]=exports.standardNames[l+e.glyphNameIndex[l]];break;case 3:this.names=[]}}function addGlyphNames(e){for(var l,r=e.tables.cmap.glyphIndexMap,a=Object.keys(r),s=0;s<a.length;s+=1){var i=a[s],o=r[i];l=e.glyphs.get(o),l.addUnicode(parseInt(i))}for(s=0;s<e.glyphs.length;s+=1)l=e.glyphs.get(s),e.cffEncoding?l.name=e.cffEncoding.charset[s]:e.glyphNames.names&&(l.name=e.glyphNames.glyphIndexToName(s))}var cffStandardStrings=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","266 ff","onedotenleader","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall","001.000","001.001","001.002","001.003","Black","Bold","Book","Light","Medium","Regular","Roman","Semibold"],cffStandardEncoding=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","","endash","dagger","daggerdbl","periodcentered","","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","","questiondown","","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","","ring","cedilla","","hungarumlaut","ogonek","caron","emdash","","","","","","","","","","","","","","","","","AE","","ordfeminine","","","","","Lslash","Oslash","OE","ordmasculine","","","","","","ae","","","","dotlessi","","","lslash","oslash","oe","germandbls"],cffExpertEncoding=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclamsmall","Hungarumlautsmall","","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","","asuperior","bsuperior","centsuperior","dsuperior","esuperior","","","isuperior","","","lsuperior","msuperior","nsuperior","osuperior","","","rsuperior","ssuperior","tsuperior","","ff","fi","fl","ffi","ffl","parenleftinferior","","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdownsmall","centoldstyle","Lslashsmall","","","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","","Dotaccentsmall","","","Macronsmall","","","figuredash","hypheninferior","","","Ogoneksmall","Ringsmall","Cedillasmall","","","","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","","","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"],standardNames=[".notdef",".null","nonmarkingreturn","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quotesingle","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","grave","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","Adieresis","Aring","Ccedilla","Eacute","Ntilde","Odieresis","Udieresis","aacute","agrave","acircumflex","adieresis","atilde","aring","ccedilla","eacute","egrave","ecircumflex","edieresis","iacute","igrave","icircumflex","idieresis","ntilde","oacute","ograve","ocircumflex","odieresis","otilde","uacute","ugrave","ucircumflex","udieresis","dagger","degree","cent","sterling","section","bullet","paragraph","germandbls","registered","copyright","trademark","acute","dieresis","notequal","AE","Oslash","infinity","plusminus","lessequal","greaterequal","yen","mu","partialdiff","summation","product","pi","integral","ordfeminine","ordmasculine","Omega","ae","oslash","questiondown","exclamdown","logicalnot","radical","florin","approxequal","Delta","guillemotleft","guillemotright","ellipsis","nonbreakingspace","Agrave","Atilde","Otilde","OE","oe","endash","emdash","quotedblleft","quotedblright","quoteleft","quoteright","divide","lozenge","ydieresis","Ydieresis","fraction","currency","guilsinglleft","guilsinglright","fi","fl","daggerdbl","periodcentered","quotesinglbase","quotedblbase","perthousand","Acircumflex","Ecircumflex","Aacute","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Oacute","Ocircumflex","apple","Ograve","Uacute","Ucircumflex","Ugrave","dotlessi","circumflex","tilde","macron","breve","dotaccent","ring","cedilla","hungarumlaut","ogonek","caron","Lslash","lslash","Scaron","scaron","Zcaron","zcaron","brokenbar","Eth","eth","Yacute","yacute","Thorn","thorn","minus","multiply","onesuperior","twosuperior","threesuperior","onehalf","onequarter","threequarters","franc","Gbreve","gbreve","Idotaccent","Scedilla","scedilla","Cacute","cacute","Ccaron","ccaron","dcroat"];DefaultEncoding.prototype.charToGlyphIndex=function(e){var l=e.charCodeAt(0),r=this.font.glyphs;if(!r)return null;for(var a=0;a<r.length;a+=1)for(var s=r.get(a),i=0;i<s.unicodes.length;i+=1)if(s.unicodes[i]===l)return a},CmapEncoding.prototype.charToGlyphIndex=function(e){return this.cmap.glyphIndexMap[e.charCodeAt(0)]||0},CffEncoding.prototype.charToGlyphIndex=function(e){var l=e.charCodeAt(0),r=this.encoding[l];return this.charset.indexOf(r)},GlyphNames.prototype.nameToGlyphIndex=function(e){return this.names.indexOf(e)},GlyphNames.prototype.glyphIndexToName=function(e){return this.names[e]},exports.cffStandardStrings=cffStandardStrings,exports.cffStandardEncoding=cffStandardEncoding,exports.cffExpertEncoding=cffExpertEncoding,exports.standardNames=standardNames,exports.DefaultEncoding=DefaultEncoding,exports.CmapEncoding=CmapEncoding,exports.CffEncoding=CffEncoding,exports.GlyphNames=GlyphNames,exports.addGlyphNames=addGlyphNames;

},{}],5:[function(require,module,exports){
"use strict";function Font(e){e=e||{},e.empty||(util.checkArgument(e.familyName,"When creating a new Font object, familyName is required."),util.checkArgument(e.styleName,"When creating a new Font object, styleName is required."),util.checkArgument(e.unitsPerEm,"When creating a new Font object, unitsPerEm is required."),util.checkArgument(e.ascender,"When creating a new Font object, ascender is required."),util.checkArgument(e.descender,"When creating a new Font object, descender is required."),util.checkArgument(e.descender<0,"Descender should be negative (e.g. -512)."),this.names={fontFamily:{en:e.familyName||" "},fontSubfamily:{en:e.styleName||" "},fullName:{en:e.fullName||e.familyName+" "+e.styleName},postScriptName:{en:e.postScriptName||e.familyName+e.styleName},designer:{en:e.designer||" "},designerURL:{en:e.designerURL||" "},manufacturer:{en:e.manufacturer||" "},manufacturerURL:{en:e.manufacturerURL||" "},license:{en:e.license||" "},licenseURL:{en:e.licenseURL||" "},version:{en:e.version||"Version 0.1"},description:{en:e.description||" "},copyright:{en:e.copyright||" "},trademark:{en:e.trademark||" "}},this.unitsPerEm=e.unitsPerEm||1e3,this.ascender=e.ascender,this.descender=e.descender,this.createdTimestamp=e.createdTimestamp,this.tables={os2:{usWeightClass:e.weightClass||this.usWeightClasses.MEDIUM,usWidthClass:e.widthClass||this.usWidthClasses.MEDIUM,fsSelection:e.fsSelection||this.fsSelectionValues.REGULAR}}),this.supported=!0,this.glyphs=new glyphset.GlyphSet(this,e.glyphs||[]),this.encoding=new encoding.DefaultEncoding(this),this.substitution=new Substitution(this),this.tables=this.tables||{}}var path=require("./path"),sfnt=require("./tables/sfnt"),encoding=require("./encoding"),glyphset=require("./glyphset"),Substitution=require("./substitution"),util=require("./util");Font.prototype.hasChar=function(e){return null!==this.encoding.charToGlyphIndex(e)},Font.prototype.charToGlyphIndex=function(e){return this.encoding.charToGlyphIndex(e)},Font.prototype.charToGlyph=function(e){var t=this.charToGlyphIndex(e),n=this.glyphs.get(t);return n||(n=this.glyphs.get(0)),n},Font.prototype.stringToGlyphs=function(e){for(var t=[],n=0;n<e.length;n+=1){var i=e[n];t.push(this.charToGlyph(i))}return t},Font.prototype.nameToGlyphIndex=function(e){return this.glyphNames.nameToGlyphIndex(e)},Font.prototype.nameToGlyph=function(e){var t=this.nametoGlyphIndex(e),n=this.glyphs.get(t);return n||(n=this.glyphs.get(0)),n},Font.prototype.glyphIndexToName=function(e){return this.glyphNames.glyphIndexToName?this.glyphNames.glyphIndexToName(e):""},Font.prototype.getKerningValue=function(e,t){e=e.index||e,t=t.index||t;var n=this.getGposKerningValue;return n?n(e,t):this.kerningPairs[e+","+t]||0},Font.prototype.forEachGlyph=function(e,t,n,i,r,o){t=void 0!==t?t:0,n=void 0!==n?n:0,i=void 0!==i?i:72,r=r||{};for(var s=void 0===r.kerning?!0:r.kerning,a=1/this.unitsPerEm*i,h=this.stringToGlyphs(e),u=0;u<h.length;u+=1){var c=h[u];if(o(c,t,n,i,r),c.advanceWidth&&(t+=c.advanceWidth*a),s&&u<h.length-1){var l=this.getKerningValue(c,h[u+1]);t+=l*a}}},Font.prototype.getPath=function(e,t,n,i,r){var o=new path.Path;return this.forEachGlyph(e,t,n,i,r,function(e,t,n,i){var r=e.getPath(t,n,i);o.extend(r)}),o},Font.prototype.getPaths=function(e,t,n,i,r){var o=[];return this.forEachGlyph(e,t,n,i,r,function(e,t,n,i){var r=e.getPath(t,n,i);o.push(r)}),o},Font.prototype.draw=function(e,t,n,i,r,o){this.getPath(t,n,i,r,o).draw(e)},Font.prototype.drawPoints=function(e,t,n,i,r,o){this.forEachGlyph(t,n,i,r,o,function(t,n,i,r){t.drawPoints(e,n,i,r)})},Font.prototype.drawMetrics=function(e,t,n,i,r,o){this.forEachGlyph(t,n,i,r,o,function(t,n,i,r){t.drawMetrics(e,n,i,r)})},Font.prototype.getEnglishName=function(e){var t=this.names[e];return t?t.en:void 0},Font.prototype.validate=function(){function e(e,t){e||n.push(t)}function t(t){var n=i.getEnglishName(t);e(n&&n.trim().length>0,"No English "+t+" specified.")}var n=[],i=this;t("fontFamily"),t("weightName"),t("manufacturer"),t("copyright"),t("version"),e(this.unitsPerEm>0,"No unitsPerEm specified.")},Font.prototype.toTables=function(){return sfnt.fontToTable(this)},Font.prototype.toBuffer=function(){return console.warn("Font.toBuffer is deprecated. Use Font.toArrayBuffer instead."),this.toArrayBuffer()},Font.prototype.toArrayBuffer=function(){for(var e=this.toTables(),t=e.encode(),n=new ArrayBuffer(t.length),i=new Uint8Array(n),r=0;r<t.length;r++)i[r]=t[r];return n},Font.prototype.download=function(){var e=this.getEnglishName("fontFamily"),t=this.getEnglishName("fontSubfamily"),n=e.replace(/\s/g,"")+"-"+t+".otf",i=this.toArrayBuffer();if(util.isBrowser())window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem,window.requestFileSystem(window.TEMPORARY,i.byteLength,function(e){e.root.getFile(n,{create:!0},function(e){e.createWriter(function(t){var n=new DataView(i),r=new Blob([n],{type:"font/opentype"});t.write(r),t.addEventListener("writeend",function(){location.href=e.toURL()},!1)})})},function(e){throw new Error(e.name+": "+e.message)});else{var r=require("fs"),o=util.arrayBufferToNodeBuffer(i);r.writeFileSync(n,o)}},Font.prototype.fsSelectionValues={ITALIC:1,UNDERSCORE:2,NEGATIVE:4,OUTLINED:8,STRIKEOUT:16,BOLD:32,REGULAR:64,USER_TYPO_METRICS:128,WWS:256,OBLIQUE:512},Font.prototype.usWidthClasses={ULTRA_CONDENSED:1,EXTRA_CONDENSED:2,CONDENSED:3,SEMI_CONDENSED:4,MEDIUM:5,SEMI_EXPANDED:6,EXPANDED:7,EXTRA_EXPANDED:8,ULTRA_EXPANDED:9},Font.prototype.usWeightClasses={THIN:100,EXTRA_LIGHT:200,LIGHT:300,NORMAL:400,MEDIUM:500,SEMI_BOLD:600,BOLD:700,EXTRA_BOLD:800,BLACK:900},exports.Font=Font;

},{"./encoding":4,"./glyphset":7,"./path":11,"./substitution":12,"./tables/sfnt":31,"./util":33,"fs":undefined}],6:[function(require,module,exports){
"use strict";function getPathDefinition(t,i){var e=i||{commands:[]};return{configurable:!0,get:function(){return"function"==typeof e&&(e=e()),e},set:function(t){e=t}}}function Glyph(t){this.bindConstructorValues(t)}var check=require("./check"),draw=require("./draw"),path=require("./path");Glyph.prototype.bindConstructorValues=function(t){this.index=t.index||0,this.name=t.name||null,this.unicode=t.unicode||void 0,this.unicodes=t.unicodes||void 0!==t.unicode?[t.unicode]:[],t.xMin&&(this.xMin=t.xMin),t.yMin&&(this.yMin=t.yMin),t.xMax&&(this.xMax=t.xMax),t.yMax&&(this.yMax=t.yMax),t.advanceWidth&&(this.advanceWidth=t.advanceWidth),Object.defineProperty(this,"path",getPathDefinition(this,t.path))},Glyph.prototype.addUnicode=function(t){0===this.unicodes.length&&(this.unicode=t),this.unicodes.push(t)},Glyph.prototype.getPath=function(t,i,e,n){t=void 0!==t?t:0,i=void 0!==i?i:0,n=void 0!==n?n:{xScale:1,yScale:1},e=void 0!==e?e:72;for(var a=1/this.path.unitsPerEm*e,h=n.xScale*a,o=n.yScale*a,r=new path.Path,s=this.path.commands,p=0;p<s.length;p+=1){var y=s[p];"M"===y.type?r.moveTo(t+y.x*h,i+-y.y*o):"L"===y.type?r.lineTo(t+y.x*h,i+-y.y*o):"Q"===y.type?r.quadraticCurveTo(t+y.x1*h,i+-y.y1*o,t+y.x*h,i+-y.y*o):"C"===y.type?r.curveTo(t+y.x1*h,i+-y.y1*o,t+y.x2*h,i+-y.y2*o,t+y.x*h,i+-y.y*o):"Z"===y.type&&r.closePath()}return r},Glyph.prototype.getContours=function(){if(void 0===this.points)return[];for(var t=[],i=[],e=0;e<this.points.length;e+=1){var n=this.points[e];i.push(n),n.lastPointOfContour&&(t.push(i),i=[])}return check.argument(0===i.length,"There are still points left in the current contour."),t},Glyph.prototype.getMetrics=function(){for(var t=this.path.commands,i=[],e=[],n=0;n<t.length;n+=1){var a=t[n];"Z"!==a.type&&(i.push(a.x),e.push(a.y)),("Q"===a.type||"C"===a.type)&&(i.push(a.x1),e.push(a.y1)),"C"===a.type&&(i.push(a.x2),e.push(a.y2))}var h={xMin:Math.min.apply(null,i),yMin:Math.min.apply(null,e),xMax:Math.max.apply(null,i),yMax:Math.max.apply(null,e),leftSideBearing:this.leftSideBearing};return isFinite(h.xMin)||(h.xMin=0),isFinite(h.xMax)||(h.xMax=this.advanceWidth),isFinite(h.yMin)||(h.yMin=0),isFinite(h.yMax)||(h.yMax=0),h.rightSideBearing=this.advanceWidth-h.leftSideBearing-(h.xMax-h.xMin),h},Glyph.prototype.draw=function(t,i,e,n,a){this.getPath(i,e,n,a).draw(t)},Glyph.prototype.drawPoints=function(t,i,e,n){function a(i,e,n,a){var h=2*Math.PI;t.beginPath();for(var o=0;o<i.length;o+=1)t.moveTo(e+i[o].x*a,n+i[o].y*a),t.arc(e+i[o].x*a,n+i[o].y*a,2,0,h,!1);t.closePath(),t.fill()}i=void 0!==i?i:0,e=void 0!==e?e:0,n=void 0!==n?n:24;for(var h=1/this.path.unitsPerEm*n,o=[],r=[],s=this.path,p=0;p<s.commands.length;p+=1){var y=s.commands[p];void 0!==y.x&&o.push({x:y.x,y:-y.y}),void 0!==y.x1&&r.push({x:y.x1,y:-y.y1}),void 0!==y.x2&&r.push({x:y.x2,y:-y.y2})}t.fillStyle="blue",a(o,i,e,h),t.fillStyle="red",a(r,i,e,h)},Glyph.prototype.drawMetrics=function(t,i,e,n){var a;i=void 0!==i?i:0,e=void 0!==e?e:0,n=void 0!==n?n:24,a=1/this.path.unitsPerEm*n,t.lineWidth=1,t.strokeStyle="black",draw.line(t,i,-1e4,i,1e4),draw.line(t,-1e4,e,1e4,e);var h=this.xMin||0,o=this.yMin||0,r=this.xMax||0,s=this.yMax||0,p=this.advanceWidth||0;t.strokeStyle="blue",draw.line(t,i+h*a,-1e4,i+h*a,1e4),draw.line(t,i+r*a,-1e4,i+r*a,1e4),draw.line(t,-1e4,e+-o*a,1e4,e+-o*a),draw.line(t,-1e4,e+-s*a,1e4,e+-s*a),t.strokeStyle="green",draw.line(t,i+p*a,-1e4,i+p*a,1e4)},exports.Glyph=Glyph;

},{"./check":2,"./draw":3,"./path":11}],7:[function(require,module,exports){
"use strict";function defineDependentProperty(e,t,n){Object.defineProperty(e,t,{get:function(){return e.path,e[n]},set:function(t){e[n]=t},enumerable:!0,configurable:!0})}function GlyphSet(e,t){if(this.font=e,this.glyphs={},Array.isArray(t))for(var n=0;n<t.length;n++)this.glyphs[n]=t[n];this.length=t&&t.length||0}function glyphLoader(e,t){return new _glyph.Glyph({index:t,font:e})}function ttfGlyphLoader(e,t,n,r,p,h){return function(){var i=new _glyph.Glyph({index:t,font:e});return i.path=function(){n(i,r,p);var t=h(e.glyphs,i);return t.unitsPerEm=e.unitsPerEm,t},defineDependentProperty(i,"xMin","_xMin"),defineDependentProperty(i,"xMax","_xMax"),defineDependentProperty(i,"yMin","_yMin"),defineDependentProperty(i,"yMax","_yMax"),i}}function cffGlyphLoader(e,t,n,r){return function(){var p=new _glyph.Glyph({index:t,font:e});return p.path=function(){var t=n(e,p,r);return t.unitsPerEm=e.unitsPerEm,t},p}}var _glyph=require("./glyph");GlyphSet.prototype.get=function(e){return"function"==typeof this.glyphs[e]&&(this.glyphs[e]=this.glyphs[e]()),this.glyphs[e]},GlyphSet.prototype.push=function(e,t){this.glyphs[e]=t,this.length++},exports.GlyphSet=GlyphSet,exports.glyphLoader=glyphLoader,exports.ttfGlyphLoader=ttfGlyphLoader,exports.cffGlyphLoader=cffGlyphLoader;

},{"./glyph":6}],8:[function(require,module,exports){
"use strict";function searchTag(e,r){for(var t=0,a=e.length-1;a>=t;){var s=t+a>>>1,n=e[s].tag;if(n===r)return s;r>n?t=s+1:a=s-1}return-t-1}function binSearch(e,r){for(var t=0,a=e.length-1;a>=t;){var s=t+a>>>1,n=e[s];if(n===r)return s;r>n?t=s+1:a=s-1}return-t-1}var check=require("./check"),Layout={searchTag:searchTag,binSearch:binSearch,getScriptNames:function(){var e=this.getGsubTable();return e?e.scripts.map(function(e){return e.tag}):[]},getScriptTable:function(e,r){var t=this.getGsubTable(r);if(t){var a=t.scripts,s=searchTag(t.scripts,e);if(s>=0)return a[s].script;var n={tag:e,script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}};return a.splice(-1-s,0,n.script),n}},getLangSysTable:function(e,r,t){var a=this.getScriptTable(e,t);if(a){if("DFLT"===r)return a.defaultLangSys;var s=searchTag(a.langSysRecords,r);if(s>=0)return a.langSysRecords[s].langSys;if(t){var n={tag:r,langSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]}};return a.langSysRecords.splice(-1-s,0,n),n.langSys}}},getFeatureTable:function(e,r,t,a){var s=this.getLangSysTable(e,r,a);if(s){for(var n,u=s.featureIndexes,i=this.font.tables.gsub.features,g=0;g<u.length;g++)if(n=i[u[g]],n.tag===t)return n.feature;if(a){var o=i.length;return check.assert(0===o||t>=i[o-1].tag,"Features must be added in alphabetical order."),n={tag:t,feature:{params:0,lookupListIndexes:[]}},i.push(n),u.push(o),n.feature}}},getLookupTable:function(e,r,t,a,s){var n=this.getFeatureTable(e,r,t,s);if(n){for(var u,i=n.lookupListIndexes,g=this.font.tables.gsub.lookups,o=0;o<i.length;o++)if(u=g[i[o]],u.lookupType===a)return u;if(s){u={lookupType:a,lookupFlag:0,subtables:[],markFilteringSet:void 0};var f=g.length;return g.push(u),i.push(f),u}}},expandCoverage:function(e){if(1===e.format)return e.glyphs;for(var r=[],t=e.ranges,a=0;t>a;a++)for(var s=t[a],n=s.start,u=s.end,i=n;u>=i;i++)r.push(i);return r}};module.exports=Layout;

},{"./check":2}],9:[function(require,module,exports){
"use strict";function loadFromFile(e,a){var r=require("fs");r.readFile(e,function(e,r){return e?a(e.message):void a(null,util.nodeBufferToArrayBuffer(r))})}function loadFromUrl(e,a){var r=new XMLHttpRequest;r.open("get",e,!0),r.responseType="arraybuffer",r.onload=function(){return 200!==r.status?a("Font could not be loaded: "+r.statusText):a(null,r.response)},r.send()}function parseOpenTypeTableEntries(e,a){for(var r=[],s=12,t=0;a>t;t+=1){var n=parse.getTag(e,s),o=parse.getULong(e,s+4),p=parse.getULong(e,s+8),l=parse.getULong(e,s+12);r.push({tag:n,checksum:o,offset:p,length:l,compression:!1}),s+=16}return r}function parseWOFFTableEntries(e,a){for(var r=[],s=44,t=0;a>t;t+=1){var n,o=parse.getTag(e,s),p=parse.getULong(e,s+4),l=parse.getULong(e,s+8),f=parse.getULong(e,s+12);n=f>l?"WOFF":!1,r.push({tag:o,offset:p,compression:n,compressedLength:l,originalLength:f}),s+=20}return r}function uncompressTable(e,a){if("WOFF"===a.compression){var r=new Uint8Array(e.buffer,a.offset+2,a.compressedLength-2),s=new Uint8Array(a.originalLength);if(inflate(r,s),s.byteLength!==a.originalLength)throw new Error("Decompression error: "+a.tag+" decompressed length doesn't match recorded length");var t=new DataView(s.buffer,0);return{data:t,offset:0}}return{data:e,offset:a.offset}}function parseBuffer(e){var a,r,s,t=new _font.Font({empty:!0}),n=new DataView(e,0),o=[],p=parse.getTag(n,0);if(p===String.fromCharCode(0,1,0,0))t.outlinesFormat="truetype",s=parse.getUShort(n,4),o=parseOpenTypeTableEntries(n,s);else if("OTTO"===p)t.outlinesFormat="cff",s=parse.getUShort(n,4),o=parseOpenTypeTableEntries(n,s);else{if("wOFF"!==p)throw new Error("Unsupported OpenType signature "+p);var l=parse.getTag(n,4);if(l===String.fromCharCode(0,1,0,0))t.outlinesFormat="truetype";else{if("OTTO"!==l)throw new Error("Unsupported OpenType flavor "+p);t.outlinesFormat="cff"}s=parse.getUShort(n,12),o=parseWOFFTableEntries(n,s)}for(var f,u,i,c,b,m,d,g,h,T,y=0;s>y;y+=1){var v,F=o[y];switch(F.tag){case"cmap":v=uncompressTable(n,F),t.tables.cmap=cmap.parse(v.data,v.offset),t.encoding=new encoding.CmapEncoding(t.tables.cmap);break;case"fvar":u=F;break;case"head":v=uncompressTable(n,F),t.tables.head=head.parse(v.data,v.offset),t.unitsPerEm=t.tables.head.unitsPerEm,a=t.tables.head.indexToLocFormat;break;case"hhea":v=uncompressTable(n,F),t.tables.hhea=hhea.parse(v.data,v.offset),t.ascender=t.tables.hhea.ascender,t.descender=t.tables.hhea.descender,t.numberOfHMetrics=t.tables.hhea.numberOfHMetrics;break;case"hmtx":m=F;break;case"ltag":v=uncompressTable(n,F),r=ltag.parse(v.data,v.offset);break;case"maxp":v=uncompressTable(n,F),t.tables.maxp=maxp.parse(v.data,v.offset),t.numGlyphs=t.tables.maxp.numGlyphs;break;case"name":h=F;break;case"OS/2":v=uncompressTable(n,F),t.tables.os2=os2.parse(v.data,v.offset);break;case"post":v=uncompressTable(n,F),t.tables.post=post.parse(v.data,v.offset),t.glyphNames=new encoding.GlyphNames(t.tables.post);break;case"glyf":i=F;break;case"loca":g=F;break;case"CFF ":f=F;break;case"kern":d=F;break;case"GPOS":c=F;break;case"GSUB":b=F;break;case"meta":T=F}}var q=uncompressTable(n,h);if(t.tables.name=_name.parse(q.data,q.offset,r),t.names=t.tables.name,i&&g){var k=0===a,w=uncompressTable(n,g),x=loca.parse(w.data,w.offset,t.numGlyphs,k),O=uncompressTable(n,i);t.glyphs=glyf.parse(O.data,O.offset,x,t)}else{if(!f)throw new Error("Font doesn't contain TrueType or CFF outlines.");var U=uncompressTable(n,f);cff.parse(U.data,U.offset,t)}var L=uncompressTable(n,m);if(hmtx.parse(L.data,L.offset,t.numberOfHMetrics,t.numGlyphs,t.glyphs),encoding.addGlyphNames(t),d){var E=uncompressTable(n,d);t.kerningPairs=kern.parse(E.data,E.offset)}else t.kerningPairs={};if(c){var S=uncompressTable(n,c);gpos.parse(S.data,S.offset,t)}if(b){var G=uncompressTable(n,b);t.tables.gsub=gsub.parse(G.data,G.offset)}if(u){var B=uncompressTable(n,u);t.tables.fvar=fvar.parse(B.data,B.offset,t.names)}if(T){var C=uncompressTable(n,T);t.tables.meta=meta.parse(C.data,C.offset),t.metas=t.tables.meta}return t}function load(e,a){var r="undefined"==typeof window,s=r?loadFromFile:loadFromUrl;s(e,function(e,r){if(e)return a(e);var s;try{s=parseBuffer(r)}catch(t){return a(t,null)}return a(null,s)})}function loadSync(e){var a=require("fs"),r=a.readFileSync(e);return parseBuffer(util.nodeBufferToArrayBuffer(r))}var inflate=require("tiny-inflate"),encoding=require("./encoding"),_font=require("./font"),glyph=require("./glyph"),parse=require("./parse"),path=require("./path"),util=require("./util"),cmap=require("./tables/cmap"),cff=require("./tables/cff"),fvar=require("./tables/fvar"),glyf=require("./tables/glyf"),gpos=require("./tables/gpos"),gsub=require("./tables/gsub"),head=require("./tables/head"),hhea=require("./tables/hhea"),hmtx=require("./tables/hmtx"),kern=require("./tables/kern"),ltag=require("./tables/ltag"),loca=require("./tables/loca"),maxp=require("./tables/maxp"),_name=require("./tables/name"),os2=require("./tables/os2"),post=require("./tables/post"),meta=require("./tables/meta");exports._parse=parse,exports.Font=_font.Font,exports.Glyph=glyph.Glyph,exports.Path=path.Path,exports.parse=parseBuffer,exports.load=load,exports.loadSync=loadSync;
},{"./encoding":4,"./font":5,"./glyph":6,"./parse":10,"./path":11,"./tables/cff":14,"./tables/cmap":15,"./tables/fvar":16,"./tables/glyf":17,"./tables/gpos":18,"./tables/gsub":19,"./tables/head":20,"./tables/hhea":21,"./tables/hmtx":22,"./tables/kern":23,"./tables/loca":24,"./tables/ltag":25,"./tables/maxp":26,"./tables/meta":27,"./tables/name":28,"./tables/os2":29,"./tables/post":30,"./util":33,"fs":undefined,"tiny-inflate":1}],10:[function(require,module,exports){
"use strict";function getUShort(t,r){return t.getUint16(r,!1)}function Parser(t,r){this.data=t,this.offset=r,this.relativeOffset=0}var check=require("./check");exports.getByte=function(t,r){return t.getUint8(r)},exports.getCard8=exports.getByte,exports.getUShort=exports.getCard16=getUShort,exports.getShort=function(t,r){return t.getInt16(r,!1)},exports.getULong=function(t,r){return t.getUint32(r,!1)},exports.getFixed=function(t,r){var e=t.getInt16(r,!1),s=t.getUint16(r+2,!1);return e+s/65535},exports.getTag=function(t,r){for(var e="",s=r;r+4>s;s+=1)e+=String.fromCharCode(t.getInt8(s));return e},exports.getOffset=function(t,r,e){for(var s=0,a=0;e>a;a+=1)s<<=8,s+=t.getUint8(r+a);return s},exports.getBytes=function(t,r,e){for(var s=[],a=r;e>a;a+=1)s.push(t.getUint8(a));return s},exports.bytesToString=function(t){for(var r="",e=0;e<t.length;e+=1)r+=String.fromCharCode(t[e]);return r};var typeOffsets={"byte":1,uShort:2,"short":2,uLong:4,fixed:4,longDateTime:8,tag:4};Parser.prototype.parseByte=function(){var t=this.data.getUint8(this.offset+this.relativeOffset);return this.relativeOffset+=1,t},Parser.prototype.parseChar=function(){var t=this.data.getInt8(this.offset+this.relativeOffset);return this.relativeOffset+=1,t},Parser.prototype.parseCard8=Parser.prototype.parseByte,Parser.prototype.parseUShort=function(){var t=this.data.getUint16(this.offset+this.relativeOffset);return this.relativeOffset+=2,t},Parser.prototype.parseCard16=Parser.prototype.parseUShort,Parser.prototype.parseSID=Parser.prototype.parseUShort,Parser.prototype.parseOffset16=Parser.prototype.parseUShort,Parser.prototype.parseShort=function(){var t=this.data.getInt16(this.offset+this.relativeOffset);return this.relativeOffset+=2,t},Parser.prototype.parseF2Dot14=function(){var t=this.data.getInt16(this.offset+this.relativeOffset)/16384;return this.relativeOffset+=2,t},Parser.prototype.parseULong=function(){var t=exports.getULong(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,t},Parser.prototype.parseFixed=function(){var t=exports.getFixed(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,t},Parser.prototype.parseString=function(t){var r=this.data,e=this.offset+this.relativeOffset,s="";this.relativeOffset+=t;for(var a=0;t>a;a++)s+=String.fromCharCode(r.getUint8(e+a));return s},Parser.prototype.parseTag=function(){return this.parseString(4)},Parser.prototype.parseLongDateTime=function(){var t=exports.getULong(this.data,this.offset+this.relativeOffset+4);return t-=2082844800,this.relativeOffset+=8,t},Parser.prototype.parseVersion=function(){var t=getUShort(this.data,this.offset+this.relativeOffset),r=getUShort(this.data,this.offset+this.relativeOffset+2);return this.relativeOffset+=4,t+r/4096/10},Parser.prototype.skip=function(t,r){void 0===r&&(r=1),this.relativeOffset+=typeOffsets[t]*r},Parser.prototype.parseOffset16List=Parser.prototype.parseUShortList=function(t){void 0===t&&(t=this.parseUShort());for(var r=new Array(t),e=this.data,s=this.offset+this.relativeOffset,a=0;t>a;a++)r[a]=e.getUint16(s),s+=2;return this.relativeOffset+=2*t,r},Parser.prototype.parseList=function(t,r){r||(r=t,t=this.parseUShort());for(var e=new Array(t),s=0;t>s;s++)e[s]=r.call(this);return e},Parser.prototype.parseRecordList=function(t,r){r||(r=t,t=this.parseUShort());for(var e=new Array(t),s=Object.keys(r),a=0;t>a;a++){for(var o={},i=0;i<s.length;i++){var n=s[i],f=r[n];o[n]=f.call(this)}e[a]=o}return e},Parser.prototype.parseStruct=function(t){if("function"==typeof t)return t.call(this);for(var r=Object.keys(t),e={},s=0;s<r.length;s++){var a=r[s],o=t[a];e[a]=o.call(this)}return e},Parser.prototype.parsePointer=function(t){var r=this.parseOffset16();return r>0?new Parser(this.data,this.offset+r).parseStruct(t):void 0},Parser.prototype.parseListOfLists=function(t){for(var r=this.parseOffset16List(),e=r.length,s=this.relativeOffset,a=new Array(e),o=0;e>o;o++){var i=r[o];if(0!==i)if(this.relativeOffset=i,t){for(var n=this.parseOffset16List(),f=new Array(n.length),p=0;p<n.length;p++)this.relativeOffset=i+n[p],f[p]=t.call(this);a[o]=f}else a[o]=this.parseUShortList();else a[o]=void 0}return this.relativeOffset=s,a},Parser.prototype.parseCoverage=function(){var t=this.offset+this.relativeOffset,r=this.parseUShort(),e=this.parseUShort();if(1===r)return{format:1,glyphs:this.parseUShortList(e)};if(2===r){for(var s=new Array(e),a=0;e>a;a++)s[a]={start:this.parseUShort(),end:this.parseUShort(),index:this.parseUShort()};return{format:2,ranges:s}}check.assert(!1,"0x"+t.toString(16)+": Coverage format must be 1 or 2.")},Parser.prototype.parseClassDef=function(){var t=this.offset+this.relativeOffset,r=this.parseUShort();return 1===r?{format:1,startGlyph:this.parseUShort(),classes:this.parseUShortList()}:2===r?{format:2,ranges:this.parseRecordList({start:Parser.uShort,end:Parser.uShort,classId:Parser.uShort})}:void check.assert(!1,"0x"+t.toString(16)+": ClassDef format must be 1 or 2.")},Parser.list=function(t,r){return function(){return this.parseList(t,r)}},Parser.recordList=function(t,r){return function(){return this.parseRecordList(t,r)}},Parser.pointer=function(t){return function(){return this.parsePointer(t)}},Parser.tag=Parser.prototype.parseTag,Parser.byte=Parser.prototype.parseByte,Parser.uShort=Parser.offset16=Parser.prototype.parseUShort,Parser.uShortList=Parser.prototype.parseUShortList,Parser.struct=Parser.prototype.parseStruct,Parser.coverage=Parser.prototype.parseCoverage,Parser.classDef=Parser.prototype.parseClassDef;var langSysTable={reserved:Parser.uShort,reqFeatureIndex:Parser.uShort,featureIndexes:Parser.uShortList};Parser.prototype.parseScriptList=function(){return this.parsePointer(Parser.recordList({tag:Parser.tag,script:Parser.pointer({defaultLangSys:Parser.pointer(langSysTable),langSysRecords:Parser.recordList({tag:Parser.tag,langSys:Parser.pointer(langSysTable)})})}))},Parser.prototype.parseFeatureList=function(){return this.parsePointer(Parser.recordList({tag:Parser.tag,feature:Parser.pointer({featureParams:Parser.offset16,lookupListIndexes:Parser.uShortList})}))},Parser.prototype.parseLookupList=function(t){return this.parsePointer(Parser.list(Parser.pointer(function(){var r=this.parseUShort();check.argument(r>=1&&8>=r,"GSUB lookup type "+r+" unknown.");var e=this.parseUShort(),s=16&e;return{lookupType:r,lookupFlag:e,subtables:this.parseList(Parser.pointer(t[r])),markFilteringSet:s?this.parseUShort():void 0}})))},exports.Parser=Parser;

},{"./check":2}],11:[function(require,module,exports){
"use strict";function Path(){this.commands=[],this.fill="black",this.stroke=null,this.strokeWidth=1}Path.prototype.moveTo=function(t,o){this.commands.push({type:"M",x:t,y:o})},Path.prototype.lineTo=function(t,o){this.commands.push({type:"L",x:t,y:o})},Path.prototype.curveTo=Path.prototype.bezierCurveTo=function(t,o,e,i,s,h){this.commands.push({type:"C",x1:t,y1:o,x2:e,y2:i,x:s,y:h})},Path.prototype.quadTo=Path.prototype.quadraticCurveTo=function(t,o,e,i){this.commands.push({type:"Q",x1:t,y1:o,x:e,y:i})},Path.prototype.close=Path.prototype.closePath=function(){this.commands.push({type:"Z"})},Path.prototype.extend=function(t){t.commands&&(t=t.commands),Array.prototype.push.apply(this.commands,t)},Path.prototype.draw=function(t){t.beginPath();for(var o=0;o<this.commands.length;o+=1){var e=this.commands[o];"M"===e.type?t.moveTo(e.x,e.y):"L"===e.type?t.lineTo(e.x,e.y):"C"===e.type?t.bezierCurveTo(e.x1,e.y1,e.x2,e.y2,e.x,e.y):"Q"===e.type?t.quadraticCurveTo(e.x1,e.y1,e.x,e.y):"Z"===e.type&&t.closePath()}this.fill&&(t.fillStyle=this.fill,t.fill()),this.stroke&&(t.strokeStyle=this.stroke,t.lineWidth=this.strokeWidth,t.stroke())},Path.prototype.toPathData=function(t){function o(o){return Math.round(o)===o?""+Math.round(o):o.toFixed(t)}function e(){for(var t="",e=0;e<arguments.length;e+=1){var i=arguments[e];i>=0&&e>0&&(t+=" "),t+=o(i)}return t}t=void 0!==t?t:2;for(var i="",s=0;s<this.commands.length;s+=1){var h=this.commands[s];"M"===h.type?i+="M"+e(h.x,h.y):"L"===h.type?i+="L"+e(h.x,h.y):"C"===h.type?i+="C"+e(h.x1,h.y1,h.x2,h.y2,h.x,h.y):"Q"===h.type?i+="Q"+e(h.x1,h.y1,h.x,h.y):"Z"===h.type&&(i+="Z")}return i},Path.prototype.toSVG=function(t){var o='<path d="';return o+=this.toPathData(t),o+='"',this.fill&&"black"!==this.fill&&(o+=null===this.fill?' fill="none"':' fill="'+this.fill+'"'),this.stroke&&(o+=' stroke="'+this.stroke+'" stroke-width="'+this.strokeWidth+'"'),o+="/>"},exports.Path=Path;

},{}],12:[function(require,module,exports){
"use strict";function arraysEqual(t,e){var r=t.length;if(r!==e.length)return!1;for(var a=0;r>a;a++)if(t[a]!==e[a])return!1;return!0}function getSubstFormat(t,e,r){for(var a=t.subtables,s=0;s<a.length;s++){var o=a[s];if(o.substFormat===e)return o}return r?(a.push(r),r):void 0}var check=require("./check"),Layout=require("./layout"),Substitution=function(t){this.font=t};Substitution.prototype=Layout,Substitution.prototype.getGsubTable=function(t){var e=this.font.tables.gsub;return!e&&t&&(this.font.tables.gsub=e={version:1,scripts:[{tag:"DFLT",script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}}],features:[],lookups:[]}),e},Substitution.prototype.getSingle=function(t,e,r){var a=[],s=this.getLookupTable(e,r,t,1);if(!s)return a;for(var o=s.subtables,u=0;u<o.length;u++){var i,n=o[u],g=this.expandCoverage(n.coverage);if(1===n.substFormat){var l=n.deltaGlyphId;for(i=0;i<g.length;i++){var c=g[i];a.push({sub:c,by:c+l})}}else{var b=n.substitute;for(i=0;i<g.length;i++)a.push({sub:g[i],by:b[i]})}}return a},Substitution.prototype.getAlternates=function(t,e,r){var a=[],s=this.getLookupTable(e,r,t,3);if(!s)return a;for(var o=s.subtables,u=0;u<o.length;u++)for(var i=o[u],n=this.expandCoverage(i.coverage),g=i.alternateSets,l=0;l<n.length;l++)a.push({sub:n[l],by:g[l]});return a},Substitution.prototype.getLigatures=function(t,e,r){var a=[],s=this.getLookupTable(e,r,t,4);if(!s)return[];for(var o=s.subtables,u=0;u<o.length;u++)for(var i=o[u],n=this.expandCoverage(i.coverage),g=i.ligatureSets,l=0;l<n.length;l++)for(var c=n[l],b=g[l],h=0;h<b.length;h++){var p=b[h];a.push({sub:[c].concat(p.components),by:p.ligGlyph})}return a},Substitution.prototype.addSingle=function(t,e,r,a){var s=this.getLookupTable(r,a,t,1,!0),o=getSubstFormat(s,2,{substFormat:2,coverage:{format:1,glyphs:[]},substitute:[]});check.assert(1===o.coverage.format,"Ligature: unable to modify coverage table format "+o.coverage.format);var u=e.sub,i=this.binSearch(o.coverage.glyphs,u);0>i&&(i=-1-i,o.coverage.glyphs.splice(i,0,u),o.substitute.splice(i,0,0)),o.substitute[i]=e.by},Substitution.prototype.addAlternate=function(t,e,r,a){var s=this.getLookupTable(r,a,t,3,!0),o=getSubstFormat(s,1,{substFormat:1,coverage:{format:1,glyphs:[]},alternateSets:[]});check.assert(1===o.coverage.format,"Ligature: unable to modify coverage table format "+o.coverage.format);var u=e.sub,i=this.binSearch(o.coverage.glyphs,u);0>i&&(i=-1-i,o.coverage.glyphs.splice(i,0,u),o.alternateSets.splice(i,0,0)),o.alternateSets[i]=e.by},Substitution.prototype.addLigature=function(t,e,r,a){r=r||"DFLT",a=a||"DFLT";var s=this.getLookupTable(r,a,t,4,!0),o=s.subtables[0];o||(o={substFormat:1,coverage:{format:1,glyphs:[]},ligatureSets:[]},s.subtables[0]=o),check.assert(1===o.coverage.format,"Ligature: unable to modify coverage table format "+o.coverage.format);var u=e.sub[0],i=e.sub.slice(1),n={ligGlyph:e.by,components:i},g=this.binSearch(o.coverage.glyphs,u);if(g>=0){for(var l=o.ligatureSets[g],c=0;c<l.length;c++)if(arraysEqual(l[c].components,i))return;l.push(n)}else g=-1-g,o.coverage.glyphs.splice(g,0,u),o.ligatureSets.splice(g,0,[n])},Substitution.prototype.getFeature=function(t,e,r){if(e=e||"DFLT",r=r||"DFLT",/ss\d\d/.test(t))return this.getSingle(t,e,r);switch(t){case"aalt":case"salt":return this.getSingle(t,e,r).concat(this.getAlternates(t,e,r));case"dlig":case"liga":case"rlig":return this.getLigatures(t,e,r)}},Substitution.prototype.add=function(t,e,r,a){if(r=r||"DFLT",a=a||"DFLT",/ss\d\d/.test(t))return this.addSingle(t,e,r,a);switch(t){case"aalt":case"salt":return"number"==typeof e.by?this.addSingle(t,e,r,a):this.addAlternate(t,e,r,a);case"dlig":case"liga":case"rlig":return this.addLigature(t,e,r,a)}},module.exports=Substitution;

},{"./check":2,"./layout":8}],13:[function(require,module,exports){
"use strict";function Table(e,t,a){var r;for(r=0;r<t.length;r+=1){var o=t[r];this[o.name]=o.value}if(this.tableName=e,this.fields=t,a){var s=Object.keys(a);for(r=0;r<s.length;r+=1){var n=s[r],u=a[n];void 0!==this[n]&&(this[n]=u)}}}function ushortList(e,t,a){void 0===a&&(a=t.length);var r=new Array(t.length+1);r[0]={name:e+"Count",type:"USHORT",value:a};for(var o=0;o<t.length;o++)r[o+1]={name:e+o,type:"USHORT",value:t[o]};return r}function tableList(e,t,a){var r=t.length,o=new Array(r+1);o[0]={name:e+"Count",type:"USHORT",value:r};for(var s=0;r>s;s++)o[s+1]={name:e+s,type:"TABLE",value:a(t[s],s)};return o}function recordList(e,t,a){var r=t.length,o=[];o[0]={name:e+"Count",type:"USHORT",value:r};for(var s=0;r>s;s++)o=o.concat(a(t[s],s));return o}function Coverage(e){1===e.format?Table.call(this,"coverageTable",[{name:"coverageFormat",type:"USHORT",value:1}].concat(ushortList("glyph",e.glyphs))):check.assert(!1,"Can't create coverage table format 2 yet.")}function ScriptList(e){Table.call(this,"scriptListTable",recordList("scriptRecord",e,function(e,t){var a=e.script,r=a.defaultLangSys;return check.assert(!!r,"Unable to write GSUB: script "+e.tag+" has no default language system."),[{name:"scriptTag"+t,type:"TAG",value:e.tag},{name:"script"+t,type:"TABLE",value:new Table("scriptTable",[{name:"defaultLangSys",type:"TABLE",value:new Table("defaultLangSys",[{name:"lookupOrder",type:"USHORT",value:0},{name:"reqFeatureIndex",type:"USHORT",value:r.reqFeatureIndex}].concat(ushortList("featureIndex",r.featureIndexes)))}].concat(recordList("langSys",a.langSysRecords,function(e,t){var a=e.langSys;return[{name:"langSysTag"+t,type:"TAG",value:e.tag},{name:"langSys"+t,type:"TABLE",value:new Table("langSys",[{name:"lookupOrder",type:"USHORT",value:0},{name:"reqFeatureIndex",type:"USHORT",value:a.reqFeatureIndex}].concat(ushortList("featureIndex",a.featureIndexes)))}]})))}]}))}function FeatureList(e){Table.call(this,"featureListTable",recordList("featureRecord",e,function(e,t){var a=e.feature;return[{name:"featureTag"+t,type:"TAG",value:e.tag},{name:"feature"+t,type:"TABLE",value:new Table("featureTable",[{name:"featureParams",type:"USHORT",value:a.featureParams}].concat(ushortList("lookupListIndex",a.lookupListIndexes)))}]}))}function LookupList(e,t){Table.call(this,"lookupListTable",tableList("lookup",e,function(e){var a=t[e.lookupType];return check.assert(!!a,"Unable to write GSUB lookup type "+e.lookupType+" tables."),new Table("lookupTable",[{name:"lookupType",type:"USHORT",value:e.lookupType},{name:"lookupFlag",type:"USHORT",value:e.lookupFlag}].concat(tableList("subtable",e.subtables,a)))}))}var check=require("./check"),encode=require("./types").encode,sizeOf=require("./types").sizeOf;Table.prototype.encode=function(){return encode.TABLE(this)},Table.prototype.sizeOf=function(){return sizeOf.TABLE(this)},Coverage.prototype=Object.create(Table.prototype),Coverage.prototype.constructor=Coverage,ScriptList.prototype=Object.create(Table.prototype),ScriptList.prototype.constructor=ScriptList,FeatureList.prototype=Object.create(Table.prototype),FeatureList.prototype.constructor=FeatureList,LookupList.prototype=Object.create(Table.prototype),LookupList.prototype.constructor=LookupList,exports.Record=exports.Table=Table,exports.Coverage=Coverage,exports.ScriptList=ScriptList,exports.FeatureList=FeatureList,exports.LookupList=LookupList,exports.ushortList=ushortList,exports.tableList=tableList,exports.recordList=recordList;

},{"./check":2,"./types":32}],14:[function(require,module,exports){
"use strict";function equals(e,t){if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(var a=0;a<e.length;a+=1)if(!equals(e[a],t[a]))return!1;return!0}return!1}function parseCFFIndex(e,t,a){var r,n,s,i=[],o=[],h=parse.getCard16(e,t);if(0!==h){var f=parse.getByte(e,t+2);n=t+(h+1)*f+2;var p=t+3;for(r=0;h+1>r;r+=1)i.push(parse.getOffset(e,p,f)),p+=f;s=n+i[h]}else s=t+2;for(r=0;r<i.length-1;r+=1){var u=parse.getBytes(e,n+i[r],n+i[r+1]);a&&(u=a(u)),o.push(u)}return{objects:o,startOffset:t,endOffset:s}}function parseFloatOperand(e){for(var t="",a=15,r=["0","1","2","3","4","5","6","7","8","9",".","E","E-",null,"-"];;){var n=e.parseByte(),s=n>>4,i=15&n;if(s===a)break;if(t+=r[s],i===a)break;t+=r[i]}return parseFloat(t)}function parseOperand(e,t){var a,r,n,s;if(28===t)return a=e.parseByte(),r=e.parseByte(),a<<8|r;if(29===t)return a=e.parseByte(),r=e.parseByte(),n=e.parseByte(),s=e.parseByte(),a<<24|r<<16|n<<8|s;if(30===t)return parseFloatOperand(e);if(t>=32&&246>=t)return t-139;if(t>=247&&250>=t)return a=e.parseByte(),256*(t-247)+a+108;if(t>=251&&254>=t)return a=e.parseByte(),256*-(t-251)-a-108;throw new Error("Invalid b0 "+t)}function entriesToObject(e){for(var t={},a=0;a<e.length;a+=1){var r,n=e[a][0],s=e[a][1];if(r=1===s.length?s[0]:s,t.hasOwnProperty(n))throw new Error("Object "+t+" already has key "+n);t[n]=r}return t}function parseCFFDict(e,t,a){t=void 0!==t?t:0;var r=new parse.Parser(e,t),n=[],s=[];for(a=void 0!==a?a:e.length;r.relativeOffset<a;){var i=r.parseByte();21>=i?(12===i&&(i=1200+r.parseByte()),n.push([i,s]),s=[]):s.push(parseOperand(r,i))}return entriesToObject(n)}function getCFFString(e,t){return t=390>=t?encoding.cffStandardStrings[t]:e[t-391]}function interpretDict(e,t,a){for(var r={},n=0;n<t.length;n+=1){var s=t[n],i=e[s.op];void 0===i&&(i=void 0!==s.value?s.value:null),"SID"===s.type&&(i=getCFFString(a,i)),r[s.name]=i}return r}function parseCFFHeader(e,t){var a={};return a.formatMajor=parse.getCard8(e,t),a.formatMinor=parse.getCard8(e,t+1),a.size=parse.getCard8(e,t+2),a.offsetSize=parse.getCard8(e,t+3),a.startOffset=t,a.endOffset=t+4,a}function parseCFFTopDict(e,t){var a=parseCFFDict(e,0,e.byteLength);return interpretDict(a,TOP_DICT_META,t)}function parseCFFPrivateDict(e,t,a,r){var n=parseCFFDict(e,t,a);return interpretDict(n,PRIVATE_DICT_META,r)}function parseCFFCharset(e,t,a,r){var n,s,i,o=new parse.Parser(e,t);a-=1;var h=[".notdef"],f=o.parseCard8();if(0===f)for(n=0;a>n;n+=1)s=o.parseSID(),h.push(getCFFString(r,s));else if(1===f)for(;h.length<=a;)for(s=o.parseSID(),i=o.parseCard8(),n=0;i>=n;n+=1)h.push(getCFFString(r,s)),s+=1;else{if(2!==f)throw new Error("Unknown charset format "+f);for(;h.length<=a;)for(s=o.parseSID(),i=o.parseCard16(),n=0;i>=n;n+=1)h.push(getCFFString(r,s)),s+=1}return h}function parseCFFEncoding(e,t,a){var r,n,s={},i=new parse.Parser(e,t),o=i.parseCard8();if(0===o){var h=i.parseCard8();for(r=0;h>r;r+=1)n=i.parseCard8(),s[n]=r}else{if(1!==o)throw new Error("Unknown encoding format "+o);var f=i.parseCard8();for(n=1,r=0;f>r;r+=1)for(var p=i.parseCard8(),u=i.parseCard8(),l=p;p+u>=l;l+=1)s[l]=n,n+=1}return new encoding.CffEncoding(s,a)}function parseCFFCharstring(e,t,a){function r(e,t){g&&p.closePath(),p.moveTo(e,t),g=!0}function n(){var t;t=u.length%2!==0,t&&!c&&(d=u.shift()+e.nominalWidthX),l+=u.length>>1,u.length=0,c=!0}function s(a){for(var y,b,C,T,F,I,D,x,k,S,E,O,R=0;R<a.length;){var M=a[R];switch(R+=1,M){case 1:n();break;case 3:n();break;case 4:u.length>1&&!c&&(d=u.shift()+e.nominalWidthX,c=!0),m+=u.pop(),r(v,m);break;case 5:for(;u.length>0;)v+=u.shift(),m+=u.shift(),p.lineTo(v,m);break;case 6:for(;u.length>0&&(v+=u.shift(),p.lineTo(v,m),0!==u.length);)m+=u.shift(),p.lineTo(v,m);break;case 7:for(;u.length>0&&(m+=u.shift(),p.lineTo(v,m),0!==u.length);)v+=u.shift(),p.lineTo(v,m);break;case 8:for(;u.length>0;)i=v+u.shift(),o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),v=h+u.shift(),m=f+u.shift(),p.curveTo(i,o,h,f,v,m);break;case 10:F=u.pop()+e.subrsBias,I=e.subrs[F],I&&s(I);break;case 11:return;case 12:switch(M=a[R],R+=1,M){case 35:i=v+u.shift(),o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),D=h+u.shift(),x=f+u.shift(),k=D+u.shift(),S=x+u.shift(),E=k+u.shift(),O=S+u.shift(),v=E+u.shift(),m=O+u.shift(),u.shift(),p.curveTo(i,o,h,f,D,x),p.curveTo(k,S,E,O,v,m);break;case 34:i=v+u.shift(),o=m,h=i+u.shift(),f=o+u.shift(),D=h+u.shift(),x=f,k=D+u.shift(),S=f,E=k+u.shift(),O=m,v=E+u.shift(),p.curveTo(i,o,h,f,D,x),p.curveTo(k,S,E,O,v,m);break;case 36:i=v+u.shift(),o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),D=h+u.shift(),x=f,k=D+u.shift(),S=f,E=k+u.shift(),O=S+u.shift(),v=E+u.shift(),p.curveTo(i,o,h,f,D,x),p.curveTo(k,S,E,O,v,m);break;case 37:i=v+u.shift(),o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),D=h+u.shift(),x=f+u.shift(),k=D+u.shift(),S=x+u.shift(),E=k+u.shift(),O=S+u.shift(),Math.abs(E-v)>Math.abs(O-m)?v=E+u.shift():m=O+u.shift(),p.curveTo(i,o,h,f,D,x),p.curveTo(k,S,E,O,v,m);break;default:console.log("Glyph "+t.index+": unknown operator 1200"+M),u.length=0}break;case 14:u.length>0&&!c&&(d=u.shift()+e.nominalWidthX,c=!0),g&&(p.closePath(),g=!1);break;case 18:n();break;case 19:case 20:n(),R+=l+7>>3;break;case 21:u.length>2&&!c&&(d=u.shift()+e.nominalWidthX,c=!0),m+=u.pop(),v+=u.pop(),r(v,m);break;case 22:u.length>1&&!c&&(d=u.shift()+e.nominalWidthX,c=!0),v+=u.pop(),r(v,m);break;case 23:n();break;case 24:for(;u.length>2;)i=v+u.shift(),o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),v=h+u.shift(),m=f+u.shift(),p.curveTo(i,o,h,f,v,m);v+=u.shift(),m+=u.shift(),p.lineTo(v,m);break;case 25:for(;u.length>6;)v+=u.shift(),m+=u.shift(),p.lineTo(v,m);i=v+u.shift(),o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),v=h+u.shift(),m=f+u.shift(),p.curveTo(i,o,h,f,v,m);break;case 26:for(u.length%2&&(v+=u.shift());u.length>0;)i=v,o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),v=h,m=f+u.shift(),p.curveTo(i,o,h,f,v,m);break;case 27:for(u.length%2&&(m+=u.shift());u.length>0;)i=v+u.shift(),o=m,h=i+u.shift(),f=o+u.shift(),v=h+u.shift(),m=f,p.curveTo(i,o,h,f,v,m);break;case 28:y=a[R],b=a[R+1],u.push((y<<24|b<<16)>>16),R+=2;break;case 29:F=u.pop()+e.gsubrsBias,I=e.gsubrs[F],I&&s(I);break;case 30:for(;u.length>0&&(i=v,o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),v=h+u.shift(),m=f+(1===u.length?u.shift():0),p.curveTo(i,o,h,f,v,m),0!==u.length);)i=v+u.shift(),o=m,h=i+u.shift(),f=o+u.shift(),m=f+u.shift(),v=h+(1===u.length?u.shift():0),p.curveTo(i,o,h,f,v,m);break;case 31:for(;u.length>0&&(i=v+u.shift(),o=m,h=i+u.shift(),f=o+u.shift(),m=f+u.shift(),v=h+(1===u.length?u.shift():0),p.curveTo(i,o,h,f,v,m),0!==u.length);)i=v,o=m+u.shift(),h=i+u.shift(),f=o+u.shift(),v=h+u.shift(),m=f+(1===u.length?u.shift():0),p.curveTo(i,o,h,f,v,m);break;default:32>M?console.log("Glyph "+t.index+": unknown operator "+M):247>M?u.push(M-139):251>M?(y=a[R],R+=1,u.push(256*(M-247)+y+108)):255>M?(y=a[R],R+=1,u.push(256*-(M-251)-y-108)):(y=a[R],b=a[R+1],C=a[R+2],T=a[R+3],R+=4,u.push((y<<24|b<<16|C<<8|T)/65536))}}}var i,o,h,f,p=new path.Path,u=[],l=0,c=!1,d=e.defaultWidthX,g=!1,v=0,m=0;return s(a),t.advanceWidth=d,p}function calcCFFSubroutineBias(e){var t;return t=e.length<1240?107:e.length<33900?1131:32768}function parseCFFTable(e,t,a){a.tables.cff={};var r=parseCFFHeader(e,t),n=parseCFFIndex(e,r.endOffset,parse.bytesToString),s=parseCFFIndex(e,n.endOffset),i=parseCFFIndex(e,s.endOffset,parse.bytesToString),o=parseCFFIndex(e,i.endOffset);a.gsubrs=o.objects,a.gsubrsBias=calcCFFSubroutineBias(a.gsubrs);var h=new DataView(new Uint8Array(s.objects[0]).buffer),f=parseCFFTopDict(h,i.objects);a.tables.cff.topDict=f;var p=t+f["private"][1],u=parseCFFPrivateDict(e,p,f["private"][0],i.objects);if(a.defaultWidthX=u.defaultWidthX,a.nominalWidthX=u.nominalWidthX,0!==u.subrs){var l=p+u.subrs,c=parseCFFIndex(e,l);a.subrs=c.objects,a.subrsBias=calcCFFSubroutineBias(a.subrs)}else a.subrs=[],a.subrsBias=0;var d=parseCFFIndex(e,t+f.charStrings);a.nGlyphs=d.objects.length;var g=parseCFFCharset(e,t+f.charset,a.nGlyphs,i.objects);a.cffEncoding=0===f.encoding?new encoding.CffEncoding(encoding.cffStandardEncoding,g):1===f.encoding?new encoding.CffEncoding(encoding.cffExpertEncoding,g):parseCFFEncoding(e,t+f.encoding,g),a.encoding=a.encoding||a.cffEncoding,a.glyphs=new glyphset.GlyphSet(a);for(var v=0;v<a.nGlyphs;v+=1){var m=d.objects[v];a.glyphs.push(v,glyphset.cffGlyphLoader(a,v,parseCFFCharstring,m))}}function encodeString(e,t){var a,r=encoding.cffStandardStrings.indexOf(e);return r>=0&&(a=r),r=t.indexOf(e),r>=0?a=r+encoding.cffStandardStrings.length:(a=encoding.cffStandardStrings.length+t.length,t.push(e)),a}function makeHeader(){return new table.Record("Header",[{name:"major",type:"Card8",value:1},{name:"minor",type:"Card8",value:0},{name:"hdrSize",type:"Card8",value:4},{name:"major",type:"Card8",value:1}])}function makeNameIndex(e){var t=new table.Record("Name INDEX",[{name:"names",type:"INDEX",value:[]}]);t.names=[];for(var a=0;a<e.length;a+=1)t.names.push({name:"name_"+a,type:"NAME",value:e[a]});return t}function makeDict(e,t,a){for(var r={},n=0;n<e.length;n+=1){var s=e[n],i=t[s.name];void 0===i||equals(i,s.value)||("SID"===s.type&&(i=encodeString(i,a)),r[s.op]={name:s.name,type:s.type,value:i})}return r}function makeTopDict(e,t){var a=new table.Record("Top DICT",[{name:"dict",type:"DICT",value:{}}]);return a.dict=makeDict(TOP_DICT_META,e,t),a}function makeTopDictIndex(e){var t=new table.Record("Top DICT INDEX",[{name:"topDicts",type:"INDEX",value:[]}]);return t.topDicts=[{name:"topDict_0",type:"TABLE",value:e}],t}function makeStringIndex(e){var t=new table.Record("String INDEX",[{name:"strings",type:"INDEX",value:[]}]);t.strings=[];for(var a=0;a<e.length;a+=1)t.strings.push({name:"string_"+a,type:"STRING",value:e[a]});return t}function makeGlobalSubrIndex(){return new table.Record("Global Subr INDEX",[{name:"subrs",type:"INDEX",value:[]}])}function makeCharsets(e,t){for(var a=new table.Record("Charsets",[{name:"format",type:"Card8",value:0}]),r=0;r<e.length;r+=1){var n=e[r],s=encodeString(n,t);a.fields.push({name:"glyph_"+r,type:"SID",value:s})}return a}function glyphToOps(e){var t=[],a=e.path;t.push({name:"width",type:"NUMBER",value:e.advanceWidth});for(var r=0,n=0,s=0;s<a.commands.length;s+=1){var i,o,h=a.commands[s];if("Q"===h.type){var f=1/3,p=2/3;h={type:"C",x:h.x,y:h.y,x1:f*r+p*h.x1,y1:f*n+p*h.y1,x2:f*h.x+p*h.x1,y2:f*h.y+p*h.y1}}if("M"===h.type)i=Math.round(h.x-r),o=Math.round(h.y-n),t.push({name:"dx",type:"NUMBER",value:i}),t.push({name:"dy",type:"NUMBER",value:o}),t.push({name:"rmoveto",type:"OP",value:21}),r=Math.round(h.x),n=Math.round(h.y);else if("L"===h.type)i=Math.round(h.x-r),o=Math.round(h.y-n),t.push({name:"dx",type:"NUMBER",value:i}),t.push({name:"dy",type:"NUMBER",value:o}),t.push({name:"rlineto",type:"OP",value:5}),r=Math.round(h.x),n=Math.round(h.y);else if("C"===h.type){var u=Math.round(h.x1-r),l=Math.round(h.y1-n),c=Math.round(h.x2-h.x1),d=Math.round(h.y2-h.y1);i=Math.round(h.x-h.x2),o=Math.round(h.y-h.y2),t.push({name:"dx1",type:"NUMBER",value:u}),t.push({name:"dy1",type:"NUMBER",value:l}),t.push({name:"dx2",type:"NUMBER",value:c}),t.push({name:"dy2",type:"NUMBER",value:d}),t.push({name:"dx",type:"NUMBER",value:i}),t.push({name:"dy",type:"NUMBER",value:o}),t.push({name:"rrcurveto",type:"OP",value:8}),r=Math.round(h.x),n=Math.round(h.y)}}return t.push({name:"endchar",type:"OP",value:14}),t}function makeCharStringsIndex(e){for(var t=new table.Record("CharStrings INDEX",[{name:"charStrings",type:"INDEX",value:[]}]),a=0;a<e.length;a+=1){var r=e.get(a),n=glyphToOps(r);t.charStrings.push({name:r.name,type:"CHARSTRING",value:n})}return t}function makePrivateDict(e,t){var a=new table.Record("Private DICT",[{name:"dict",type:"DICT",value:{}}]);return a.dict=makeDict(PRIVATE_DICT_META,e,t),a}function makeCFFTable(e,t){for(var a,r=new table.Table("CFF ",[{name:"header",type:"RECORD"},{name:"nameIndex",type:"RECORD"},{name:"topDictIndex",type:"RECORD"},{name:"stringIndex",type:"RECORD"},{name:"globalSubrIndex",type:"RECORD"},{name:"charsets",type:"RECORD"},{name:"charStringsIndex",type:"RECORD"},{name:"privateDict",type:"RECORD"}]),n=1/t.unitsPerEm,s={version:t.version,fullName:t.fullName,familyName:t.familyName,weight:t.weightName,fontBBox:t.fontBBox||[0,0,0,0],fontMatrix:[n,0,0,n,0,0],charset:999,encoding:0,charStrings:999,"private":[0,999]},i={},o=[],h=1;h<e.length;h+=1)a=e.get(h),o.push(a.name);var f=[];r.header=makeHeader(),r.nameIndex=makeNameIndex([t.postScriptName]);var p=makeTopDict(s,f);r.topDictIndex=makeTopDictIndex(p),r.globalSubrIndex=makeGlobalSubrIndex(),r.charsets=makeCharsets(o,f),r.charStringsIndex=makeCharStringsIndex(e),r.privateDict=makePrivateDict(i,f),r.stringIndex=makeStringIndex(f);var u=r.header.sizeOf()+r.nameIndex.sizeOf()+r.topDictIndex.sizeOf()+r.stringIndex.sizeOf()+r.globalSubrIndex.sizeOf();return s.charset=u,s.encoding=0,s.charStrings=s.charset+r.charsets.sizeOf(),s.private[1]=s.charStrings+r.charStringsIndex.sizeOf(),p=makeTopDict(s,f),r.topDictIndex=makeTopDictIndex(p),r}var encoding=require("../encoding"),glyphset=require("../glyphset"),parse=require("../parse"),path=require("../path"),table=require("../table"),TOP_DICT_META=[{name:"version",op:0,type:"SID"},{name:"notice",op:1,type:"SID"},{name:"copyright",op:1200,type:"SID"},{name:"fullName",op:2,type:"SID"},{name:"familyName",op:3,type:"SID"},{name:"weight",op:4,type:"SID"},{name:"isFixedPitch",op:1201,type:"number",value:0},{name:"italicAngle",op:1202,type:"number",value:0},{name:"underlinePosition",op:1203,type:"number",value:-100},{name:"underlineThickness",op:1204,type:"number",value:50},{name:"paintType",op:1205,type:"number",value:0},{name:"charstringType",op:1206,type:"number",value:2},{name:"fontMatrix",op:1207,type:["real","real","real","real","real","real"],value:[.001,0,0,.001,0,0]},{name:"uniqueId",op:13,type:"number"},{name:"fontBBox",op:5,type:["number","number","number","number"],value:[0,0,0,0]},{name:"strokeWidth",op:1208,type:"number",value:0},{name:"xuid",op:14,type:[],value:null},{name:"charset",op:15,type:"offset",value:0},{name:"encoding",op:16,type:"offset",value:0},{name:"charStrings",op:17,type:"offset",value:0},{name:"private",op:18,type:["number","offset"],value:[0,0]}],PRIVATE_DICT_META=[{name:"subrs",op:19,type:"offset",value:0},{name:"defaultWidthX",op:20,type:"number",value:0},{name:"nominalWidthX",op:21,type:"number",value:0}];exports.parse=parseCFFTable,exports.make=makeCFFTable;

},{"../encoding":4,"../glyphset":7,"../parse":10,"../path":11,"../table":13}],15:[function(require,module,exports){
"use strict";function parseCmapTableFormat12(e,a){var r;a.parseUShort(),e.length=a.parseULong(),e.language=a.parseULong();var t;for(e.groupCount=t=a.parseULong(),e.glyphIndexMap={},r=0;t>r;r+=1)for(var n=a.parseULong(),s=a.parseULong(),o=a.parseULong(),l=n;s>=l;l+=1)e.glyphIndexMap[l]=o,o++}function parseCmapTableFormat4(e,a,r,t,n){var s;e.length=a.parseUShort(),e.language=a.parseUShort();var o;e.segCount=o=a.parseUShort()>>1,a.skip("uShort",3),e.glyphIndexMap={};var l=new parse.Parser(r,t+n+14),p=new parse.Parser(r,t+n+16+2*o),g=new parse.Parser(r,t+n+16+4*o),m=new parse.Parser(r,t+n+16+6*o),u=t+n+16+8*o;for(s=0;o-1>s;s+=1)for(var h,f=l.parseUShort(),c=p.parseUShort(),d=g.parseShort(),i=m.parseUShort(),v=c;f>=v;v+=1)0!==i?(u=m.offset+m.relativeOffset-2,u+=i,u+=2*(v-c),h=parse.getUShort(r,u),0!==h&&(h=h+d&65535)):h=v+d&65535,e.glyphIndexMap[v]=h}function parseCmapTable(e,a){var r,t={};t.version=parse.getUShort(e,a),check.argument(0===t.version,"cmap table version should be 0."),t.numTables=parse.getUShort(e,a+2);var n=-1;for(r=t.numTables-1;r>=0;r-=1){var s=parse.getUShort(e,a+4+8*r),o=parse.getUShort(e,a+4+8*r+2);if(3===s&&(0===o||1===o||10===o)){n=parse.getULong(e,a+4+8*r+4);break}}if(-1===n)return null;var l=new parse.Parser(e,a+n);if(t.format=l.parseUShort(),12===t.format)parseCmapTableFormat12(t,l);else{if(4!==t.format)throw new Error("Only format 4 and 12 cmap tables are supported.");parseCmapTableFormat4(t,l,e,a,n)}return t}function addSegment(e,a,r){e.segments.push({end:a,start:a,delta:-(a-r),offset:0})}function addTerminatorSegment(e){e.segments.push({end:65535,start:65535,delta:1,offset:0})}function makeCmapTable(e){var a,r=new table.Table("cmap",[{name:"version",type:"USHORT",value:0},{name:"numTables",type:"USHORT",value:1},{name:"platformID",type:"USHORT",value:3},{name:"encodingID",type:"USHORT",value:1},{name:"offset",type:"ULONG",value:12},{name:"format",type:"USHORT",value:4},{name:"length",type:"USHORT",value:0},{name:"language",type:"USHORT",value:0},{name:"segCountX2",type:"USHORT",value:0},{name:"searchRange",type:"USHORT",value:0},{name:"entrySelector",type:"USHORT",value:0},{name:"rangeShift",type:"USHORT",value:0}]);for(r.segments=[],a=0;a<e.length;a+=1){for(var t=e.get(a),n=0;n<t.unicodes.length;n+=1)addSegment(r,t.unicodes[n],a);r.segments=r.segments.sort(function(e,a){return e.start-a.start})}addTerminatorSegment(r);var s;s=r.segments.length,r.segCountX2=2*s,r.searchRange=2*Math.pow(2,Math.floor(Math.log(s)/Math.log(2))),r.entrySelector=Math.log(r.searchRange/2)/Math.log(2),r.rangeShift=r.segCountX2-r.searchRange;var o=[],l=[],p=[],g=[],m=[];for(a=0;s>a;a+=1){var u=r.segments[a];o=o.concat({name:"end_"+a,type:"USHORT",value:u.end}),l=l.concat({name:"start_"+a,type:"USHORT",value:u.start}),p=p.concat({name:"idDelta_"+a,type:"SHORT",value:u.delta}),g=g.concat({name:"idRangeOffset_"+a,type:"USHORT",value:u.offset}),void 0!==u.glyphId&&(m=m.concat({name:"glyph_"+a,type:"USHORT",value:u.glyphId}))}return r.fields=r.fields.concat(o),r.fields.push({name:"reservedPad",type:"USHORT",value:0}),r.fields=r.fields.concat(l),r.fields=r.fields.concat(p),r.fields=r.fields.concat(g),r.fields=r.fields.concat(m),r.length=14+2*o.length+2+2*l.length+2*p.length+2*g.length+2*m.length,r}var check=require("../check"),parse=require("../parse"),table=require("../table");exports.parse=parseCmapTable,exports.make=makeCmapTable;

},{"../check":2,"../parse":10,"../table":13}],16:[function(require,module,exports){
"use strict";function addName(e,a){var r=JSON.stringify(e),n=256;for(var t in a){var s=parseInt(t);if(s&&!(256>s)){if(JSON.stringify(a[t])===r)return s;s>=n&&(n=s+1)}}return a[n]=e,n}function makeFvarAxis(e,a,r){var n=addName(a.name,r);return[{name:"tag_"+e,type:"TAG",value:a.tag},{name:"minValue_"+e,type:"FIXED",value:a.minValue<<16},{name:"defaultValue_"+e,type:"FIXED",value:a.defaultValue<<16},{name:"maxValue_"+e,type:"FIXED",value:a.maxValue<<16},{name:"flags_"+e,type:"USHORT",value:0},{name:"nameID_"+e,type:"USHORT",value:n}]}function parseFvarAxis(e,a,r){var n={},t=new parse.Parser(e,a);return n.tag=t.parseTag(),n.minValue=t.parseFixed(),n.defaultValue=t.parseFixed(),n.maxValue=t.parseFixed(),t.skip("uShort",1),n.name=r[t.parseUShort()]||{},n}function makeFvarInstance(e,a,r,n){for(var t=addName(a.name,n),s=[{name:"nameID_"+e,type:"USHORT",value:t},{name:"flags_"+e,type:"USHORT",value:0}],u=0;u<r.length;++u){var i=r[u].tag;s.push({name:"axis_"+e+" "+i,type:"FIXED",value:a.coordinates[i]<<16})}return s}function parseFvarInstance(e,a,r,n){var t={},s=new parse.Parser(e,a);t.name=n[s.parseUShort()]||{},s.skip("uShort",1),t.coordinates={};for(var u=0;u<r.length;++u)t.coordinates[r[u].tag]=s.parseFixed();return t}function makeFvarTable(e,a){var r=new table.Table("fvar",[{name:"version",type:"ULONG",value:65536},{name:"offsetToData",type:"USHORT",value:0},{name:"countSizePairs",type:"USHORT",value:2},{name:"axisCount",type:"USHORT",value:e.axes.length},{name:"axisSize",type:"USHORT",value:20},{name:"instanceCount",type:"USHORT",value:e.instances.length},{name:"instanceSize",type:"USHORT",value:4+4*e.axes.length}]);r.offsetToData=r.sizeOf();for(var n=0;n<e.axes.length;n++)r.fields=r.fields.concat(makeFvarAxis(n,e.axes[n],a));for(var t=0;t<e.instances.length;t++)r.fields=r.fields.concat(makeFvarInstance(t,e.instances[t],e.axes,a));return r}function parseFvarTable(e,a,r){var n=new parse.Parser(e,a),t=n.parseULong();check.argument(65536===t,"Unsupported fvar table version.");var s=n.parseOffset16();n.skip("uShort",1);for(var u=n.parseUShort(),i=n.parseUShort(),p=n.parseUShort(),l=n.parseUShort(),v=[],o=0;u>o;o++)v.push(parseFvarAxis(e,a+s+o*i,r));for(var m=[],f=a+s+u*i,c=0;p>c;c++)m.push(parseFvarInstance(e,f+c*l,v,r));return{axes:v,instances:m}}var check=require("../check"),parse=require("../parse"),table=require("../table");exports.make=makeFvarTable,exports.parse=parseFvarTable;

},{"../check":2,"../parse":10,"../table":13}],17:[function(require,module,exports){
"use strict";function parseGlyphCoordinate(e,r,t,o,a){var n;return(r&o)>0?(n=e.parseByte(),0===(r&a)&&(n=-n),n=t+n):n=(r&a)>0?t:t+e.parseShort(),n}function parseGlyph(e,r,t){var o=new parse.Parser(r,t);e.numberOfContours=o.parseShort(),e._xMin=o.parseShort(),e._yMin=o.parseShort(),e._xMax=o.parseShort(),e._yMax=o.parseShort();var a,n;if(e.numberOfContours>0){var s,p=e.endPointIndices=[];for(s=0;s<e.numberOfContours;s+=1)p.push(o.parseUShort());for(e.instructionLength=o.parseUShort(),e.instructions=[],s=0;s<e.instructionLength;s+=1)e.instructions.push(o.parseByte());var i=p[p.length-1]+1;for(a=[],s=0;i>s;s+=1)if(n=o.parseByte(),a.push(n),(8&n)>0)for(var h=o.parseByte(),l=0;h>l;l+=1)a.push(n),s+=1;if(check.argument(a.length===i,"Bad flags."),p.length>0){var u,c=[];if(i>0){for(s=0;i>s;s+=1)n=a[s],u={},u.onCurve=!!(1&n),u.lastPointOfContour=p.indexOf(s)>=0,c.push(u);var y=0;for(s=0;i>s;s+=1)n=a[s],u=c[s],u.x=parseGlyphCoordinate(o,n,y,2,16),y=u.x;var f=0;for(s=0;i>s;s+=1)n=a[s],u=c[s],u.y=parseGlyphCoordinate(o,n,f,4,32),f=u.y}e.points=c}else e.points=[]}else if(0===e.numberOfContours)e.points=[];else{e.isComposite=!0,e.points=[],e.components=[];for(var v=!0;v;){a=o.parseUShort();var x={glyphIndex:o.parseUShort(),xScale:1,scale01:0,scale10:0,yScale:1,dx:0,dy:0};(1&a)>0?(2&a)>0?(x.dx=o.parseShort(),x.dy=o.parseShort()):x.matchedPoints=[o.parseUShort(),o.parseUShort()]:(2&a)>0?(x.dx=o.parseChar(),x.dy=o.parseChar()):x.matchedPoints=[o.parseByte(),o.parseByte()],(8&a)>0?x.xScale=x.yScale=o.parseF2Dot14():(64&a)>0?(x.xScale=o.parseF2Dot14(),x.yScale=o.parseF2Dot14()):(128&a)>0&&(x.xScale=o.parseF2Dot14(),x.scale01=o.parseF2Dot14(),x.scale10=o.parseF2Dot14(),x.yScale=o.parseF2Dot14()),e.components.push(x),v=!!(32&a)}}}function transformPoints(e,r){for(var t=[],o=0;o<e.length;o+=1){var a=e[o],n={x:r.xScale*a.x+r.scale01*a.y+r.dx,y:r.scale10*a.x+r.yScale*a.y+r.dy,onCurve:a.onCurve,lastPointOfContour:a.lastPointOfContour};t.push(n)}return t}function getContours(e){for(var r=[],t=[],o=0;o<e.length;o+=1){var a=e[o];t.push(a),a.lastPointOfContour&&(r.push(t),t=[])}return check.argument(0===t.length,"There are still points left in the current contour."),r}function getPath(e){var r=new path.Path;if(!e)return r;for(var t=getContours(e),o=0;o<t.length;o+=1){var a,n,s=t[o],p=s[0],i=s[s.length-1];p.onCurve?(a=null,n=!0):(p=i.onCurve?i:{x:(p.x+i.x)/2,y:(p.y+i.y)/2},a=p,n=!1),r.moveTo(p.x,p.y);for(var h=n?1:0;h<s.length;h+=1){var l=s[h],u=0===h?p:s[h-1];if(u.onCurve&&l.onCurve)r.lineTo(l.x,l.y);else if(u.onCurve&&!l.onCurve)a=l;else if(u.onCurve||l.onCurve){if(u.onCurve||!l.onCurve)throw new Error("Invalid state.");r.quadraticCurveTo(a.x,a.y,l.x,l.y),a=null}else{var c={x:(u.x+l.x)/2,y:(u.y+l.y)/2};r.quadraticCurveTo(u.x,u.y,c.x,c.y),a=l}}p!==i&&(a?r.quadraticCurveTo(a.x,a.y,p.x,p.y):r.lineTo(p.x,p.y))}return r.closePath(),r}function buildPath(e,r){if(r.isComposite)for(var t=0;t<r.components.length;t+=1){var o=r.components[t],a=e.get(o.glyphIndex);if(a.getPath(),a.points){var n;if(void 0===o.matchedPoints)n=transformPoints(a.points,o);else{if(o.matchedPoints[0]>r.points.length-1||o.matchedPoints[1]>a.points.length-1)throw Error("Matched points out of range in "+r.name);var s=r.points[o.matchedPoints[0]],p=a.points[o.matchedPoints[1]],i={xScale:o.xScale,scale01:o.scale01,scale10:o.scale10,yScale:o.yScale,dx:0,dy:0};p=transformPoints([p],i)[0],i.dx=s.x-p.x,i.dy=s.y-p.y,n=transformPoints(a.points,i)}r.points=r.points.concat(n)}}return getPath(r.points)}function parseGlyfTable(e,r,t,o){var a,n=new glyphset.GlyphSet(o);for(a=0;a<t.length-1;a+=1){var s=t[a],p=t[a+1];s!==p?n.push(a,glyphset.ttfGlyphLoader(o,a,parseGlyph,e,r+s,buildPath)):n.push(a,glyphset.glyphLoader(o,a))}return n}var check=require("../check"),glyphset=require("../glyphset"),parse=require("../parse"),path=require("../path");exports.parse=parseGlyfTable;

},{"../check":2,"../glyphset":7,"../parse":10,"../path":11}],18:[function(require,module,exports){
"use strict";function parseTaggedListTable(r,e){for(var a=new parse.Parser(r,e),s=a.parseUShort(),t=[],o=0;s>o;o++)t[a.parseTag()]={offset:a.parseUShort()};return t}function parseCoverageTable(r,e){var a=new parse.Parser(r,e),s=a.parseUShort(),t=a.parseUShort();if(1===s)return a.parseUShortList(t);if(2===s){for(var o=[];t--;)for(var p=a.parseUShort(),n=a.parseUShort(),f=a.parseUShort(),i=p;n>=i;i++)o[f++]=i;return o}}function parseClassDefTable(r,e){var a=new parse.Parser(r,e),s=a.parseUShort();if(1===s){var t=a.parseUShort(),o=a.parseUShort(),p=a.parseUShortList(o);return function(r){return p[r-t]||0}}if(2===s){for(var n=a.parseUShort(),f=[],i=[],h=[],S=0;n>S;S++)f[S]=a.parseUShort(),i[S]=a.parseUShort(),h[S]=a.parseUShort();return function(r){for(var e=0,a=f.length-1;a>e;){var s=e+a+1>>1;r<f[s]?a=s-1:e=s}return f[e]<=r&&r<=i[e]?h[e]||0:0}}}function parsePairPosSubTable(r,e){var a,s,t=new parse.Parser(r,e),o=t.parseUShort(),p=t.parseUShort(),n=parseCoverageTable(r,e+p),f=t.parseUShort(),i=t.parseUShort();if(4===f&&0===i){var h={};if(1===o){for(var S=t.parseUShort(),u=[],v=t.parseOffset16List(S),U=0;S>U;U++){var l=v[U],g=h[l];if(!g){g={},t.relativeOffset=l;for(var T=t.parseUShort();T--;){var c=t.parseUShort();f&&(a=t.parseShort()),i&&(s=t.parseShort()),g[c]=a}}u[n[U]]=g}return function(r,e){var a=u[r];return a?a[e]:void 0}}if(2===o){for(var b=t.parseUShort(),P=t.parseUShort(),L=t.parseUShort(),k=t.parseUShort(),d=parseClassDefTable(r,e+b),w=parseClassDefTable(r,e+P),O=[],C=0;L>C;C++)for(var G=O[C]=[],K=0;k>K;K++)f&&(a=t.parseShort()),i&&(s=t.parseShort()),G[K]=a;var V={};for(C=0;C<n.length;C++)V[n[C]]=1;return function(r,e){if(V[r]){var a=d(r),s=w(e),t=O[a];return t?t[s]:void 0}}}}}function parseLookupTable(r,e){var a=new parse.Parser(r,e),s=a.parseUShort(),t=a.parseUShort(),o=16&t,p=a.parseUShort(),n=a.parseOffset16List(p),f={lookupType:s,lookupFlag:t,markFilteringSet:o?a.parseUShort():-1};if(2===s){for(var i=[],h=0;p>h;h++)i.push(parsePairPosSubTable(r,e+n[h]));f.getKerningValue=function(r,e){for(var a=i.length;a--;){var s=i[a](r,e);if(void 0!==s)return s}return 0}}return f}function parseGposTable(r,e,a){var s=new parse.Parser(r,e),t=s.parseFixed();check.argument(1===t,"Unsupported GPOS table version."),parseTaggedListTable(r,e+s.parseUShort()),parseTaggedListTable(r,e+s.parseUShort());var o=s.parseUShort();s.relativeOffset=o;for(var p=s.parseUShort(),n=s.parseOffset16List(p),f=e+o,i=0;p>i;i++){var h=parseLookupTable(r,f+n[i]);2!==h.lookupType||a.getGposKerningValue||(a.getGposKerningValue=h.getKerningValue)}}var check=require("../check"),parse=require("../parse");exports.parse=parseGposTable;

},{"../check":2,"../parse":10}],19:[function(require,module,exports){
"use strict";function parseGsubTable(e,t){t=t||0;var s=new Parser(e,t),r=s.parseVersion();return check.argument(1===r,"Unsupported GSUB table version."),{version:r,scripts:s.parseScriptList(),features:s.parseFeatureList(),lookups:s.parseLookupList(subtableParsers)}}function makeGsubTable(e){return new table.Table("GSUB",[{name:"version",type:"ULONG",value:65536},{name:"scripts",type:"TABLE",value:new table.ScriptList(e.scripts)},{name:"features",type:"TABLE",value:new table.FeatureList(e.features)},{name:"lookups",type:"TABLE",value:new table.LookupList(e.lookups,subtableMakers)}])}var check=require("../check"),Parser=require("../parse").Parser,subtableParsers=new Array(9),table=require("../table");subtableParsers[1]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(Parser.coverage),deltaGlyphId:this.parseUShort()}:2===t?{substFormat:2,coverage:this.parsePointer(Parser.coverage),substitute:this.parseOffset16List()}:void check.assert(!1,"0x"+e.toString(16)+": lookup type 1 format must be 1 or 2.")},subtableParsers[2]=function(){var e=this.parseUShort();return check.argument(1===e,"GSUB Multiple Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(Parser.coverage),sequences:this.parseListOfLists()}},subtableParsers[3]=function(){var e=this.parseUShort();return check.argument(1===e,"GSUB Alternate Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(Parser.coverage),alternateSets:this.parseListOfLists()}},subtableParsers[4]=function(){var e=this.parseUShort();return check.argument(1===e,"GSUB ligature table identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(Parser.coverage),ligatureSets:this.parseListOfLists(function(){return{ligGlyph:this.parseUShort(),components:this.parseUShortList(this.parseUShort()-1)}})}};var lookupRecordDesc={sequenceIndex:Parser.uShort,lookupListIndex:Parser.uShort};subtableParsers[5]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{substFormat:t,coverage:this.parsePointer(Parser.coverage),ruleSets:this.parseListOfLists(function(){var e=this.parseUShort(),t=this.parseUShort();return{input:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,lookupRecordDesc)}})};if(2===t)return{substFormat:t,coverage:this.parsePointer(Parser.coverage),classDef:this.parsePointer(Parser.classDef),classSets:this.parseListOfLists(function(){var e=this.parseUShort(),t=this.parseUShort();return{classes:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,lookupRecordDesc)}})};if(3===t){var s=this.parseUShort(),r=this.parseUShort();return{substFormat:t,coverages:this.parseList(s,Parser.pointer(Parser.coverage)),lookupRecords:this.parseRecordList(r,lookupRecordDesc)}}check.assert(!1,"0x"+e.toString(16)+": lookup type 5 format must be 1, 2 or 3.")},subtableParsers[6]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(Parser.coverage),chainRuleSets:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(lookupRecordDesc)}})}:2===t?{substFormat:2,coverage:this.parsePointer(Parser.coverage),backtrackClassDef:this.parsePointer(Parser.classDef),inputClassDef:this.parsePointer(Parser.classDef),lookaheadClassDef:this.parsePointer(Parser.classDef),chainClassSet:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(lookupRecordDesc)}})}:3===t?{substFormat:3,backtrackCoverage:this.parseList(Parser.pointer(Parser.coverage)),inputCoverage:this.parseList(Parser.pointer(Parser.coverage)),lookaheadCoverage:this.parseList(Parser.pointer(Parser.coverage)),lookupRecords:this.parseRecordList(lookupRecordDesc)}:void check.assert(!1,"0x"+e.toString(16)+": lookup type 6 format must be 1, 2 or 3.")},subtableParsers[7]=function(){var e=this.parseUShort();check.argument(1===e,"GSUB Extension Substitution subtable identifier-format must be 1");var t=this.parseUShort(),s=new Parser(this.data,this.offset+this.parseULong());return{substFormat:1,lookupType:t,extension:subtableParsers[t].call(s)}},subtableParsers[8]=function(){var e=this.parseUShort();return check.argument(1===e,"GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(Parser.coverage),backtrackCoverage:this.parseList(Parser.pointer(Parser.coverage)),lookaheadCoverage:this.parseList(Parser.pointer(Parser.coverage)),substitutes:this.parseUShortList()}};var subtableMakers=new Array(9);subtableMakers[1]=function(e){return 1===e.substFormat?new table.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new table.Coverage(e.coverage)},{name:"deltaGlyphID",type:"USHORT",value:e.deltaGlyphId}]):new table.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:2},{name:"coverage",type:"TABLE",value:new table.Coverage(e.coverage)}].concat(table.ushortList("substitute",e.substitute)))},subtableMakers[3]=function(e){return check.assert(1===e.substFormat,"Lookup type 3 substFormat must be 1."),new table.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new table.Coverage(e.coverage)}].concat(table.tableList("altSet",e.alternateSets,function(e){return new table.Table("alternateSetTable",table.ushortList("alternate",e))})))},subtableMakers[4]=function(e){return check.assert(1===e.substFormat,"Lookup type 4 substFormat must be 1."),new table.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new table.Coverage(e.coverage)}].concat(table.tableList("ligSet",e.ligatureSets,function(e){return new table.Table("ligatureSetTable",table.tableList("ligature",e,function(e){return new table.Table("ligatureTable",[{name:"ligGlyph",type:"USHORT",value:e.ligGlyph}].concat(table.ushortList("component",e.components,e.components.length+1)))}))})))},exports.parse=parseGsubTable,exports.make=makeGsubTable;

},{"../check":2,"../parse":10,"../table":13}],20:[function(require,module,exports){
"use strict";function parseHeadTable(e,a){var t={},r=new parse.Parser(e,a);return t.version=r.parseVersion(),t.fontRevision=Math.round(1e3*r.parseFixed())/1e3,t.checkSumAdjustment=r.parseULong(),t.magicNumber=r.parseULong(),check.argument(1594834165===t.magicNumber,"Font header has wrong magic number."),t.flags=r.parseUShort(),t.unitsPerEm=r.parseUShort(),t.created=r.parseLongDateTime(),t.modified=r.parseLongDateTime(),t.xMin=r.parseShort(),t.yMin=r.parseShort(),t.xMax=r.parseShort(),t.yMax=r.parseShort(),t.macStyle=r.parseUShort(),t.lowestRecPPEM=r.parseUShort(),t.fontDirectionHint=r.parseShort(),t.indexToLocFormat=r.parseShort(),t.glyphDataFormat=r.parseShort(),t}function makeHeadTable(e){var a=Math.round((new Date).getTime()/1e3)+2082844800,t=a;return e.createdTimestamp&&(t=e.createdTimestamp+2082844800),new table.Table("head",[{name:"version",type:"FIXED",value:65536},{name:"fontRevision",type:"FIXED",value:65536},{name:"checkSumAdjustment",type:"ULONG",value:0},{name:"magicNumber",type:"ULONG",value:1594834165},{name:"flags",type:"USHORT",value:0},{name:"unitsPerEm",type:"USHORT",value:1e3},{name:"created",type:"LONGDATETIME",value:t},{name:"modified",type:"LONGDATETIME",value:a},{name:"xMin",type:"SHORT",value:0},{name:"yMin",type:"SHORT",value:0},{name:"xMax",type:"SHORT",value:0},{name:"yMax",type:"SHORT",value:0},{name:"macStyle",type:"USHORT",value:0},{name:"lowestRecPPEM",type:"USHORT",value:0},{name:"fontDirectionHint",type:"SHORT",value:2},{name:"indexToLocFormat",type:"SHORT",value:0},{name:"glyphDataFormat",type:"SHORT",value:0}],e)}var check=require("../check"),parse=require("../parse"),table=require("../table");exports.parse=parseHeadTable,exports.make=makeHeadTable;

},{"../check":2,"../parse":10,"../table":13}],21:[function(require,module,exports){
"use strict";function parseHheaTable(e,a){var r={},t=new parse.Parser(e,a);return r.version=t.parseVersion(),r.ascender=t.parseShort(),r.descender=t.parseShort(),r.lineGap=t.parseShort(),r.advanceWidthMax=t.parseUShort(),r.minLeftSideBearing=t.parseShort(),r.minRightSideBearing=t.parseShort(),r.xMaxExtent=t.parseShort(),r.caretSlopeRise=t.parseShort(),r.caretSlopeRun=t.parseShort(),r.caretOffset=t.parseShort(),t.relativeOffset+=8,r.metricDataFormat=t.parseShort(),r.numberOfHMetrics=t.parseUShort(),r}function makeHheaTable(e){return new table.Table("hhea",[{name:"version",type:"FIXED",value:65536},{name:"ascender",type:"FWORD",value:0},{name:"descender",type:"FWORD",value:0},{name:"lineGap",type:"FWORD",value:0},{name:"advanceWidthMax",type:"UFWORD",value:0},{name:"minLeftSideBearing",type:"FWORD",value:0},{name:"minRightSideBearing",type:"FWORD",value:0},{name:"xMaxExtent",type:"FWORD",value:0},{name:"caretSlopeRise",type:"SHORT",value:1},{name:"caretSlopeRun",type:"SHORT",value:0},{name:"caretOffset",type:"SHORT",value:0},{name:"reserved1",type:"SHORT",value:0},{name:"reserved2",type:"SHORT",value:0},{name:"reserved3",type:"SHORT",value:0},{name:"reserved4",type:"SHORT",value:0},{name:"metricDataFormat",type:"SHORT",value:0},{name:"numberOfHMetrics",type:"USHORT",value:0}],e)}var parse=require("../parse"),table=require("../table");exports.parse=parseHheaTable,exports.make=makeHheaTable;

},{"../parse":10,"../table":13}],22:[function(require,module,exports){
"use strict";function parseHmtxTable(e,a,r,t,s){for(var i,l,n=new parse.Parser(e,a),p=0;t>p;p+=1){r>p&&(i=n.parseUShort(),l=n.parseShort());var d=s.get(p);d.advanceWidth=i,d.leftSideBearing=l}}function makeHmtxTable(e){for(var a=new table.Table("hmtx",[]),r=0;r<e.length;r+=1){var t=e.get(r),s=t.advanceWidth||0,i=t.leftSideBearing||0;a.fields.push({name:"advanceWidth_"+r,type:"USHORT",value:s}),a.fields.push({name:"leftSideBearing_"+r,type:"SHORT",value:i})}return a}var parse=require("../parse"),table=require("../table");exports.parse=parseHmtxTable,exports.make=makeHmtxTable;

},{"../parse":10,"../table":13}],23:[function(require,module,exports){
"use strict";function parseKernTable(r,e){var a={},s=new parse.Parser(r,e),p=s.parseUShort();check.argument(0===p,"Unsupported kern table version."),s.skip("uShort",1);var t=s.parseUShort();check.argument(0===t,"Unsupported kern sub-table version."),s.skip("uShort",2);var o=s.parseUShort();s.skip("uShort",3);for(var n=0;o>n;n+=1){var h=s.parseUShort(),u=s.parseUShort(),c=s.parseShort();a[h+","+u]=c}return a}var check=require("../check"),parse=require("../parse");exports.parse=parseKernTable;

},{"../check":2,"../parse":10}],24:[function(require,module,exports){
"use strict";function parseLocaTable(r,a,e,s){for(var p=new parse.Parser(r,a),o=s?p.parseUShort:p.parseULong,t=[],c=0;e+1>c;c+=1){var n=o.call(p);s&&(n*=2),t.push(n)}return t}var parse=require("../parse");exports.parse=parseLocaTable;

},{"../parse":10}],25:[function(require,module,exports){
"use strict";function makeLtagTable(e){for(var a=new table.Table("ltag",[{name:"version",type:"ULONG",value:1},{name:"flags",type:"ULONG",value:0},{name:"numTags",type:"ULONG",value:e.length}]),r="",t=12+4*e.length,n=0;n<e.length;++n){var s=r.indexOf(e[n]);0>s&&(s=r.length,r+=e[n]),a.fields.push({name:"offset "+n,type:"USHORT",value:t+s}),a.fields.push({name:"length "+n,type:"USHORT",value:e[n].length})}return a.fields.push({name:"stringPool",type:"CHARARRAY",value:r}),a}function parseLtagTable(e,a){var r=new parse.Parser(e,a),t=r.parseULong();check.argument(1===t,"Unsupported ltag table version."),r.skip("uLong",1);for(var n=r.parseULong(),s=[],l=0;n>l;l++){for(var p="",u=a+r.parseUShort(),g=r.parseUShort(),o=u;u+g>o;++o)p+=String.fromCharCode(e.getInt8(o));s.push(p)}return s}var check=require("../check"),parse=require("../parse"),table=require("../table");exports.make=makeLtagTable,exports.parse=parseLtagTable;

},{"../check":2,"../parse":10,"../table":13}],26:[function(require,module,exports){
"use strict";function parseMaxpTable(e,a){var r={},s=new parse.Parser(e,a);return r.version=s.parseVersion(),r.numGlyphs=s.parseUShort(),1===r.version&&(r.maxPoints=s.parseUShort(),r.maxContours=s.parseUShort(),r.maxCompositePoints=s.parseUShort(),r.maxCompositeContours=s.parseUShort(),r.maxZones=s.parseUShort(),r.maxTwilightPoints=s.parseUShort(),r.maxStorage=s.parseUShort(),r.maxFunctionDefs=s.parseUShort(),r.maxInstructionDefs=s.parseUShort(),r.maxStackElements=s.parseUShort(),r.maxSizeOfInstructions=s.parseUShort(),r.maxComponentElements=s.parseUShort(),r.maxComponentDepth=s.parseUShort()),r}function makeMaxpTable(e){return new table.Table("maxp",[{name:"version",type:"FIXED",value:20480},{name:"numGlyphs",type:"USHORT",value:e}])}var parse=require("../parse"),table=require("../table");exports.parse=parseMaxpTable,exports.make=makeMaxpTable;

},{"../parse":10,"../table":13}],27:[function(require,module,exports){
"use strict";function parseMetaTable(e,a){var r=new parse.Parser(e,a),t=r.parseULong();check.argument(1===t,"Unsupported META table version."),r.parseULong(),r.parseULong();for(var s=r.parseULong(),n={},p=0;s>p;p++){var l=r.parseTag(),u=r.parseULong(),o=r.parseULong(),v=decode.UTF8(e,a+u,o);n[l]=v}return n}function makeMetaTable(e){var a=Object.keys(e).length,r="",t=16+12*a,s=new table.Table("meta",[{name:"version",type:"ULONG",value:1},{name:"flags",type:"ULONG",value:0},{name:"offset",type:"ULONG",value:t},{name:"numTags",type:"ULONG",value:a}]);for(var n in e){var p=r.length;r+=e[n],s.fields.push({name:"tag "+n,type:"TAG",value:n}),s.fields.push({name:"offset "+n,type:"ULONG",value:t+p}),s.fields.push({name:"length "+n,type:"ULONG",value:e[n].length})}return s.fields.push({name:"stringPool",type:"CHARARRAY",value:r}),s}var types=require("../types"),decode=types.decode,check=require("../check"),parse=require("../parse"),table=require("../table");exports.parse=parseMetaTable,exports.make=makeMetaTable;

},{"../check":2,"../parse":10,"../table":13,"../types":32}],28:[function(require,module,exports){
"use strict";function getLanguageCode(e,a,r){switch(e){case 0:if(65535===a)return"und";if(r)return r[a];break;case 1:return macLanguages[a];case 3:return windowsLanguages[a]}return void 0}function getEncoding(e,a,r){switch(e){case 0:return utf16;case 1:return macLanguageEncodings[r]||macScriptEncodings[a];case 3:if(1===a||10===a)return utf16}return void 0}function parseNameTable(e,a,r){for(var n={},t=new parse.Parser(e,a),s=t.parseUShort(),m=t.parseUShort(),i=t.offset+t.parseUShort(),c=0;m>c;c++){var o=t.parseUShort(),u=t.parseUShort(),l=t.parseUShort(),g=t.parseUShort(),d=nameTableNames[g]||g,f=t.parseUShort(),p=t.parseUShort(),h=getLanguageCode(o,l,r),v=getEncoding(o,u,l);if(void 0!==v&&void 0!==h){var x;if(x=v===utf16?decode.UTF16(e,i+p,f):decode.MACSTRING(e,i+p,f,v)){var y=n[d];void 0===y&&(y=n[d]={}),y[h]=x}}}var b=0;return 1===s&&(b=t.parseUShort()),n}function reverseDict(e){var a={};for(var r in e)a[e[r]]=parseInt(r);return a}function makeNameRecord(e,a,r,n,t,s){return new table.Record("NameRecord",[{name:"platformID",type:"USHORT",value:e},{name:"encodingID",type:"USHORT",value:a},{name:"languageID",type:"USHORT",value:r},{name:"nameID",type:"USHORT",value:n},{name:"length",type:"USHORT",value:t},{name:"offset",type:"USHORT",value:s}])}function findSubArray(e,a){var r=e.length,n=a.length-r+1;e:for(var t=0;n>t;t++)for(;n>t;t++){for(var s=0;r>s;s++)if(a[t+s]!==e[s])continue e;return t}return-1}function addStringToPool(e,a){var r=findSubArray(e,a);if(0>r){r=a.length;for(var n=0,t=e.length;t>n;++n)a.push(e[n])}return r}function makeNameTable(e,a){var r,n=[],t={},s=reverseDict(nameTableNames);for(var m in e){var i=s[m];if(void 0===i&&(i=m),r=parseInt(i),isNaN(r))throw new Error('Name table entry "'+m+'" does not exist, see nameTableNames for complete list.');t[r]=e[m],n.push(r)}for(var c=reverseDict(macLanguages),o=reverseDict(windowsLanguages),u=[],l=[],g=0;g<n.length;g++){r=n[g];var d=t[r];for(var f in d){var p=d[f],h=1,v=c[f],x=macLanguageToScript[v],y=getEncoding(h,x,v),b=encode.MACSTRING(p,y);void 0===b&&(h=0,v=a.indexOf(f),0>v&&(v=a.length,a.push(f)),x=4,b=encode.UTF16(p));var k=addStringToPool(b,l);u.push(makeNameRecord(h,x,v,r,b.length,k));var S=o[f];if(void 0!==S){var T=encode.UTF16(p),N=addStringToPool(T,l);u.push(makeNameRecord(3,1,S,r,T.length,N))}}}u.sort(function(e,a){return e.platformID-a.platformID||e.encodingID-a.encodingID||e.languageID-a.languageID||e.nameID-a.nameID});for(var U=new table.Table("name",[{name:"format",type:"USHORT",value:0},{name:"count",type:"USHORT",value:u.length},{name:"stringOffset",type:"USHORT",value:6+12*u.length}]),I=0;I<u.length;I++)U.fields.push({name:"record_"+I,type:"RECORD",value:u[I]});return U.fields.push({name:"strings",type:"LITERAL",value:l}),U}var types=require("../types"),decode=types.decode,encode=types.encode,parse=require("../parse"),table=require("../table"),nameTableNames=["copyright","fontFamily","fontSubfamily","uniqueID","fullName","version","postScriptName","trademark","manufacturer","designer","description","manufacturerURL","designerURL","license","licenseURL","reserved","preferredFamily","preferredSubfamily","compatibleFullName","sampleText","postScriptFindFontName","wwsFamily","wwsSubfamily"],macLanguages={0:"en",1:"fr",2:"de",3:"it",4:"nl",5:"sv",6:"es",7:"da",8:"pt",9:"no",10:"he",11:"ja",12:"ar",13:"fi",14:"el",15:"is",16:"mt",17:"tr",18:"hr",19:"zh-Hant",20:"ur",21:"hi",22:"th",23:"ko",24:"lt",25:"pl",26:"hu",27:"es",28:"lv",29:"se",30:"fo",31:"fa",32:"ru",33:"zh",34:"nl-BE",35:"ga",36:"sq",37:"ro",38:"cz",39:"sk",40:"si",41:"yi",42:"sr",43:"mk",44:"bg",45:"uk",46:"be",47:"uz",48:"kk",49:"az-Cyrl",50:"az-Arab",51:"hy",52:"ka",53:"mo",54:"ky",55:"tg",56:"tk",57:"mn-CN",58:"mn",59:"ps",60:"ks",61:"ku",62:"sd",63:"bo",64:"ne",65:"sa",66:"mr",67:"bn",68:"as",69:"gu",70:"pa",71:"or",72:"ml",73:"kn",74:"ta",75:"te",76:"si",77:"my",78:"km",79:"lo",80:"vi",81:"id",82:"tl",83:"ms",84:"ms-Arab",85:"am",86:"ti",87:"om",88:"so",89:"sw",90:"rw",91:"rn",92:"ny",93:"mg",94:"eo",128:"cy",129:"eu",130:"ca",131:"la",132:"qu",133:"gn",134:"ay",135:"tt",136:"ug",137:"dz",138:"jv",139:"su",140:"gl",141:"af",142:"br",143:"iu",144:"gd",145:"gv",146:"ga",147:"to",148:"el-polyton",149:"kl",150:"az",151:"nn"},macLanguageToScript={0:0,1:0,2:0,3:0,4:0,5:0,6:0,7:0,8:0,9:0,10:5,11:1,12:4,13:0,14:6,15:0,16:0,17:0,18:0,19:2,20:4,21:9,22:21,23:3,24:29,25:29,26:29,27:29,28:29,29:0,30:0,31:4,32:7,33:25,34:0,35:0,36:0,37:0,38:29,39:29,40:0,41:5,42:7,43:7,44:7,45:7,46:7,47:7,48:7,49:7,50:4,51:24,52:23,53:7,54:7,55:7,56:7,57:27,58:7,59:4,60:4,61:4,62:4,63:26,64:9,65:9,66:9,67:13,68:13,69:11,70:10,71:12,72:17,73:16,74:14,75:15,76:18,77:19,78:20,79:22,80:30,81:0,82:0,83:0,84:4,85:28,86:28,87:28,88:0,89:0,90:0,91:0,92:0,93:0,94:0,128:0,129:0,130:0,131:0,132:0,133:0,134:0,135:7,136:4,137:26,138:0,139:0,140:0,141:0,142:0,143:28,144:0,145:0,146:0,147:0,148:6,149:0,150:0,151:0},windowsLanguages={1078:"af",1052:"sq",1156:"gsw",1118:"am",5121:"ar-DZ",15361:"ar-BH",3073:"ar",2049:"ar-IQ",11265:"ar-JO",13313:"ar-KW",12289:"ar-LB",4097:"ar-LY",6145:"ary",8193:"ar-OM",16385:"ar-QA",1025:"ar-SA",10241:"ar-SY",7169:"aeb",14337:"ar-AE",9217:"ar-YE",1067:"hy",1101:"as",2092:"az-Cyrl",1068:"az",1133:"ba",1069:"eu",1059:"be",2117:"bn",1093:"bn-IN",8218:"bs-Cyrl",5146:"bs",1150:"br",1026:"bg",1027:"ca",3076:"zh-HK",5124:"zh-MO",2052:"zh",4100:"zh-SG",1028:"zh-TW",1155:"co",1050:"hr",4122:"hr-BA",1029:"cs",1030:"da",1164:"prs",1125:"dv",2067:"nl-BE",1043:"nl",3081:"en-AU",10249:"en-BZ",4105:"en-CA",9225:"en-029",16393:"en-IN",6153:"en-IE",8201:"en-JM",17417:"en-MY",5129:"en-NZ",13321:"en-PH",18441:"en-SG",7177:"en-ZA",11273:"en-TT",2057:"en-GB",1033:"en",12297:"en-ZW",1061:"et",1080:"fo",1124:"fil",1035:"fi",2060:"fr-BE",3084:"fr-CA",1036:"fr",5132:"fr-LU",6156:"fr-MC",4108:"fr-CH",1122:"fy",1110:"gl",1079:"ka",3079:"de-AT",1031:"de",5127:"de-LI",4103:"de-LU",2055:"de-CH",1032:"el",1135:"kl",1095:"gu",1128:"ha",1037:"he",1081:"hi",1038:"hu",1039:"is",1136:"ig",1057:"id",1117:"iu",2141:"iu-Latn",2108:"ga",1076:"xh",1077:"zu",1040:"it",2064:"it-CH",1041:"ja",1099:"kn",1087:"kk",1107:"km",1158:"quc",1159:"rw",1089:"sw",1111:"kok",1042:"ko",1088:"ky",1108:"lo",1062:"lv",1063:"lt",2094:"dsb",1134:"lb",1071:"mk",2110:"ms-BN",1086:"ms",1100:"ml",1082:"mt",1153:"mi",1146:"arn",1102:"mr",1148:"moh",1104:"mn",2128:"mn-CN",1121:"ne",1044:"nb",2068:"nn",1154:"oc",1096:"or",1123:"ps",1045:"pl",1046:"pt",2070:"pt-PT",1094:"pa",1131:"qu-BO",2155:"qu-EC",3179:"qu",1048:"ro",1047:"rm",1049:"ru",9275:"smn",4155:"smj-NO",5179:"smj",3131:"se-FI",1083:"se",2107:"se-SE",8251:"sms",6203:"sma-NO",7227:"sms",1103:"sa",7194:"sr-Cyrl-BA",3098:"sr",6170:"sr-Latn-BA",2074:"sr-Latn",1132:"nso",1074:"tn",1115:"si",1051:"sk",1060:"sl",11274:"es-AR",16394:"es-BO",13322:"es-CL",9226:"es-CO",5130:"es-CR",7178:"es-DO",12298:"es-EC",17418:"es-SV",4106:"es-GT",18442:"es-HN",2058:"es-MX",19466:"es-NI",6154:"es-PA",15370:"es-PY",10250:"es-PE",20490:"es-PR",3082:"es",1034:"es",21514:"es-US",14346:"es-UY",8202:"es-VE",2077:"sv-FI",1053:"sv",1114:"syr",1064:"tg",2143:"tzm",1097:"ta",1092:"tt",1098:"te",1054:"th",1105:"bo",1055:"tr",1090:"tk",1152:"ug",1058:"uk",1070:"hsb",1056:"ur",2115:"uz-Cyrl",1091:"uz",1066:"vi",1106:"cy",1160:"wo",1157:"sah",1144:"ii",1130:"yo"},utf16="utf-16",macScriptEncodings={0:"macintosh",1:"x-mac-japanese",2:"x-mac-chinesetrad",3:"x-mac-korean",6:"x-mac-greek",7:"x-mac-cyrillic",9:"x-mac-devanagai",10:"x-mac-gurmukhi",11:"x-mac-gujarati",12:"x-mac-oriya",13:"x-mac-bengali",14:"x-mac-tamil",15:"x-mac-telugu",16:"x-mac-kannada",17:"x-mac-malayalam",18:"x-mac-sinhalese",19:"x-mac-burmese",20:"x-mac-khmer",21:"x-mac-thai",22:"x-mac-lao",23:"x-mac-georgian",24:"x-mac-armenian",25:"x-mac-chinesesimp",26:"x-mac-tibetan",27:"x-mac-mongolian",28:"x-mac-ethiopic",29:"x-mac-ce",30:"x-mac-vietnamese",31:"x-mac-extarabic"},macLanguageEncodings={15:"x-mac-icelandic",17:"x-mac-turkish",18:"x-mac-croatian",24:"x-mac-ce",25:"x-mac-ce",26:"x-mac-ce",27:"x-mac-ce",28:"x-mac-ce",30:"x-mac-icelandic",37:"x-mac-romanian",38:"x-mac-ce",39:"x-mac-ce",40:"x-mac-ce",143:"x-mac-inuit",146:"x-mac-gaelic"};exports.parse=parseNameTable,exports.make=makeNameTable;

},{"../parse":10,"../table":13,"../types":32}],29:[function(require,module,exports){
"use strict";function getUnicodeRange(e){for(var n=0;n<unicodeRanges.length;n+=1){var a=unicodeRanges[n];if(e>=a.begin&&e<a.end)return n}return-1}function parseOS2Table(e,n){var a={},i=new parse.Parser(e,n);a.version=i.parseUShort(),a.xAvgCharWidth=i.parseShort(),a.usWeightClass=i.parseUShort(),a.usWidthClass=i.parseUShort(),a.fsType=i.parseUShort(),a.ySubscriptXSize=i.parseShort(),a.ySubscriptYSize=i.parseShort(),a.ySubscriptXOffset=i.parseShort(),a.ySubscriptYOffset=i.parseShort(),a.ySuperscriptXSize=i.parseShort(),a.ySuperscriptYSize=i.parseShort(),a.ySuperscriptXOffset=i.parseShort(),a.ySuperscriptYOffset=i.parseShort(),a.yStrikeoutSize=i.parseShort(),a.yStrikeoutPosition=i.parseShort(),a.sFamilyClass=i.parseShort(),a.panose=[];for(var t=0;10>t;t++)a.panose[t]=i.parseByte();return a.ulUnicodeRange1=i.parseULong(),a.ulUnicodeRange2=i.parseULong(),a.ulUnicodeRange3=i.parseULong(),a.ulUnicodeRange4=i.parseULong(),a.achVendID=String.fromCharCode(i.parseByte(),i.parseByte(),i.parseByte(),i.parseByte()),a.fsSelection=i.parseUShort(),a.usFirstCharIndex=i.parseUShort(),a.usLastCharIndex=i.parseUShort(),a.sTypoAscender=i.parseShort(),a.sTypoDescender=i.parseShort(),a.sTypoLineGap=i.parseShort(),a.usWinAscent=i.parseUShort(),a.usWinDescent=i.parseUShort(),a.version>=1&&(a.ulCodePageRange1=i.parseULong(),a.ulCodePageRange2=i.parseULong()),a.version>=2&&(a.sxHeight=i.parseShort(),a.sCapHeight=i.parseShort(),a.usDefaultChar=i.parseUShort(),a.usBreakChar=i.parseUShort(),a.usMaxContent=i.parseUShort()),a}function makeOS2Table(e){return new table.Table("OS/2",[{name:"version",type:"USHORT",value:3},{name:"xAvgCharWidth",type:"SHORT",value:0},{name:"usWeightClass",type:"USHORT",value:0},{name:"usWidthClass",type:"USHORT",value:0},{name:"fsType",type:"USHORT",value:0},{name:"ySubscriptXSize",type:"SHORT",value:650},{name:"ySubscriptYSize",type:"SHORT",value:699},{name:"ySubscriptXOffset",type:"SHORT",value:0},{name:"ySubscriptYOffset",type:"SHORT",value:140},{name:"ySuperscriptXSize",type:"SHORT",value:650},{name:"ySuperscriptYSize",type:"SHORT",value:699},{name:"ySuperscriptXOffset",type:"SHORT",value:0},{name:"ySuperscriptYOffset",type:"SHORT",value:479},{name:"yStrikeoutSize",type:"SHORT",value:49},{name:"yStrikeoutPosition",type:"SHORT",value:258},{name:"sFamilyClass",type:"SHORT",value:0},{name:"bFamilyType",type:"BYTE",value:0},{name:"bSerifStyle",type:"BYTE",value:0},{name:"bWeight",type:"BYTE",value:0},{name:"bProportion",type:"BYTE",value:0},{name:"bContrast",type:"BYTE",value:0},{name:"bStrokeVariation",type:"BYTE",value:0},{name:"bArmStyle",type:"BYTE",value:0},{name:"bLetterform",type:"BYTE",value:0},{name:"bMidline",type:"BYTE",value:0},{name:"bXHeight",type:"BYTE",value:0},{name:"ulUnicodeRange1",type:"ULONG",value:0},{name:"ulUnicodeRange2",type:"ULONG",value:0},{name:"ulUnicodeRange3",type:"ULONG",value:0},{name:"ulUnicodeRange4",type:"ULONG",value:0},{name:"achVendID",type:"CHARARRAY",value:"XXXX"},{name:"fsSelection",type:"USHORT",value:0},{name:"usFirstCharIndex",type:"USHORT",value:0},{name:"usLastCharIndex",type:"USHORT",value:0},{name:"sTypoAscender",type:"SHORT",value:0},{name:"sTypoDescender",type:"SHORT",value:0},{name:"sTypoLineGap",type:"SHORT",value:0},{name:"usWinAscent",type:"USHORT",value:0},{name:"usWinDescent",type:"USHORT",value:0},{name:"ulCodePageRange1",type:"ULONG",value:0},{name:"ulCodePageRange2",type:"ULONG",value:0},{name:"sxHeight",type:"SHORT",value:0},{name:"sCapHeight",type:"SHORT",value:0},{name:"usDefaultChar",type:"USHORT",value:0},{name:"usBreakChar",type:"USHORT",value:0},{name:"usMaxContext",type:"USHORT",value:0}],e)}var parse=require("../parse"),table=require("../table"),unicodeRanges=[{begin:0,end:127},{begin:128,end:255},{begin:256,end:383},{begin:384,end:591},{begin:592,end:687},{begin:688,end:767},{begin:768,end:879},{begin:880,end:1023},{begin:11392,end:11519},{begin:1024,end:1279},{begin:1328,end:1423},{begin:1424,end:1535},{begin:42240,end:42559},{begin:1536,end:1791},{begin:1984,end:2047},{begin:2304,end:2431},{begin:2432,end:2559},{begin:2560,end:2687},{begin:2688,end:2815},{begin:2816,end:2943},{begin:2944,end:3071},{begin:3072,end:3199},{begin:3200,end:3327},{begin:3328,end:3455},{begin:3584,end:3711},{begin:3712,end:3839},{begin:4256,end:4351},{begin:6912,end:7039},{begin:4352,end:4607},{begin:7680,end:7935},{begin:7936,end:8191},{begin:8192,end:8303},{begin:8304,end:8351},{begin:8352,end:8399},{begin:8400,end:8447},{begin:8448,end:8527},{begin:8528,end:8591},{begin:8592,end:8703},{begin:8704,end:8959},{begin:8960,end:9215},{begin:9216,end:9279},{begin:9280,end:9311},{begin:9312,end:9471},{begin:9472,end:9599},{begin:9600,end:9631},{begin:9632,end:9727},{begin:9728,end:9983},{begin:9984,end:10175},{begin:12288,end:12351},{begin:12352,end:12447},{begin:12448,end:12543},{begin:12544,end:12591},{begin:12592,end:12687},{begin:43072,end:43135},{begin:12800,end:13055},{begin:13056,end:13311},{begin:44032,end:55215},{begin:55296,end:57343},{begin:67840,end:67871},{begin:19968,end:40959},{begin:57344,end:63743},{begin:12736,end:12783},{begin:64256,end:64335},{begin:64336,end:65023},{begin:65056,end:65071},{begin:65040,end:65055},{begin:65104,end:65135},{begin:65136,end:65279},{begin:65280,end:65519},{begin:65520,end:65535},{begin:3840,end:4095},{begin:1792,end:1871},{begin:1920,end:1983},{begin:3456,end:3583},{begin:4096,end:4255},{begin:4608,end:4991},{begin:5024,end:5119},{begin:5120,end:5759},{begin:5760,end:5791},{begin:5792,end:5887},{begin:6016,end:6143},{begin:6144,end:6319},{begin:10240,end:10495},{begin:40960,end:42127},{begin:5888,end:5919},{begin:66304,end:66351},{begin:66352,end:66383},{begin:66560,end:66639},{begin:118784,end:119039},{begin:119808,end:120831},{begin:1044480,end:1048573},{begin:65024,end:65039},{begin:917504,end:917631},{begin:6400,end:6479},{begin:6480,end:6527},{begin:6528,end:6623},{begin:6656,end:6687},{begin:11264,end:11359},{begin:11568,end:11647},{begin:19904,end:19967},{begin:43008,end:43055},{begin:65536,end:65663},{begin:65856,end:65935},{begin:66432,end:66463},{begin:66464,end:66527},{begin:66640,end:66687},{begin:66688,end:66735},{begin:67584,end:67647},{begin:68096,end:68191},{begin:119552,end:119647},{begin:73728,end:74751},{begin:119648,end:119679},{begin:7040,end:7103},{begin:7168,end:7247},{begin:7248,end:7295},{begin:43136,end:43231},{begin:43264,end:43311},{begin:43312,end:43359},{begin:43520,end:43615},{begin:65936,end:65999},{begin:66e3,end:66047},{begin:66208,end:66271},{begin:127024,end:127135}];exports.unicodeRanges=unicodeRanges,exports.getUnicodeRange=getUnicodeRange,exports.parse=parseOS2Table,exports.make=makeOS2Table;

},{"../parse":10,"../table":13}],30:[function(require,module,exports){
"use strict";function parsePostTable(e,a){var r,n={},s=new parse.Parser(e,a);switch(n.version=s.parseVersion(),n.italicAngle=s.parseFixed(),n.underlinePosition=s.parseShort(),n.underlineThickness=s.parseShort(),n.isFixedPitch=s.parseULong(),n.minMemType42=s.parseULong(),n.maxMemType42=s.parseULong(),n.minMemType1=s.parseULong(),n.maxMemType1=s.parseULong(),n.version){case 1:n.names=encoding.standardNames.slice();break;case 2:for(n.numberOfGlyphs=s.parseUShort(),n.glyphNameIndex=new Array(n.numberOfGlyphs),r=0;r<n.numberOfGlyphs;r++)n.glyphNameIndex[r]=s.parseUShort();for(n.names=[],r=0;r<n.numberOfGlyphs;r++)if(n.glyphNameIndex[r]>=encoding.standardNames.length){var p=s.parseChar();n.names.push(s.parseString(p))}break;case 2.5:for(n.numberOfGlyphs=s.parseUShort(),n.offset=new Array(n.numberOfGlyphs),r=0;r<n.numberOfGlyphs;r++)n.offset[r]=s.parseChar()}return n}function makePostTable(){return new table.Table("post",[{name:"version",type:"FIXED",value:196608},{name:"italicAngle",type:"FIXED",value:0},{name:"underlinePosition",type:"FWORD",value:0},{name:"underlineThickness",type:"FWORD",value:0},{name:"isFixedPitch",type:"ULONG",value:0},{name:"minMemType42",type:"ULONG",value:0},{name:"maxMemType42",type:"ULONG",value:0},{name:"minMemType1",type:"ULONG",value:0},{name:"maxMemType1",type:"ULONG",value:0}])}var encoding=require("../encoding"),parse=require("../parse"),table=require("../table");exports.parse=parsePostTable,exports.make=makePostTable;

},{"../encoding":4,"../parse":10,"../table":13}],31:[function(require,module,exports){
"use strict";function log2(e){return Math.log(e)/Math.log(2)|0}function computeCheckSum(e){for(;e.length%4!==0;)e.push(0);for(var a=0,n=0;n<e.length;n+=4)a+=(e[n]<<24)+(e[n+1]<<16)+(e[n+2]<<8)+e[n+3];return a%=Math.pow(2,32)}function makeTableRecord(e,a,n,t){return new table.Record("Table Record",[{name:"tag",type:"TAG",value:void 0!==e?e:""},{name:"checkSum",type:"ULONG",value:void 0!==a?a:0},{name:"offset",type:"ULONG",value:void 0!==n?n:0},{name:"length",type:"ULONG",value:void 0!==t?t:0}])}function makeSfntTable(e){var a=new table.Table("sfnt",[{name:"version",type:"TAG",value:"OTTO"},{name:"numTables",type:"USHORT",value:0},{name:"searchRange",type:"USHORT",value:0},{name:"entrySelector",type:"USHORT",value:0},{name:"rangeShift",type:"USHORT",value:0}]);a.tables=e,a.numTables=e.length;var n=Math.pow(2,log2(a.numTables));a.searchRange=16*n,a.entrySelector=log2(n),a.rangeShift=16*a.numTables-a.searchRange;for(var t=[],r=[],i=a.sizeOf()+makeTableRecord().sizeOf()*a.numTables;i%4!==0;)i+=1,r.push({name:"padding",type:"BYTE",value:0});for(var s=0;s<e.length;s+=1){var l=e[s];check.argument(4===l.tableName.length,"Table name"+l.tableName+" is invalid.");var u=l.sizeOf(),m=makeTableRecord(l.tableName,computeCheckSum(l.encode()),i,u);for(t.push({name:m.tag+" Table Record",type:"RECORD",value:m}),r.push({name:l.tableName+" table",type:"RECORD",value:l}),i+=u,check.argument(!isNaN(i),"Something went wrong calculating the offset.");i%4!==0;)i+=1,r.push({name:"padding",type:"BYTE",value:0})}return t.sort(function(e,a){return e.value.tag>a.value.tag?1:-1}),a.fields=a.fields.concat(t),a.fields=a.fields.concat(r),a}function metricsForChar(e,a,n){for(var t=0;t<a.length;t+=1){var r=e.charToGlyphIndex(a[t]);if(r>0){var i=e.glyphs.get(r);return i.getMetrics()}}return n}function average(e){for(var a=0,n=0;n<e.length;n+=1)a+=e[n];return a/e.length}function fontToSfntTable(e){for(var a,n=[],t=[],r=[],i=[],s=[],l=[],u=[],m=0,h=0,o=0,c=0,d=0,g=0;g<e.glyphs.length;g+=1){var p=e.glyphs.get(g),f=0|p.unicode;if(isNaN(p.advanceWidth))throw new Error("Glyph "+p.name+" ("+g+"): advanceWidth is not a number.");(a>f||void 0===a)&&f>0&&(a=f),f>m&&(m=f);var v=os2.getUnicodeRange(f);if(32>v)h|=1<<v;else if(64>v)o|=1<<v-32;else if(96>v)c|=1<<v-64;else{if(!(123>v))throw new Error("Unicode ranges bits > 123 are reserved for internal usage");d|=1<<v-96}if(".notdef"!==p.name){var y=p.getMetrics();n.push(y.xMin),t.push(y.yMin),r.push(y.xMax),i.push(y.yMax),l.push(y.leftSideBearing),u.push(y.rightSideBearing),s.push(p.advanceWidth)}}var x={xMin:Math.min.apply(null,n),yMin:Math.min.apply(null,t),xMax:Math.max.apply(null,r),yMax:Math.max.apply(null,i),advanceWidthMax:Math.max.apply(null,s),advanceWidthAvg:average(s),minLeftSideBearing:Math.min.apply(null,l),maxLeftSideBearing:Math.max.apply(null,l),minRightSideBearing:Math.min.apply(null,u)};x.ascender=e.ascender,x.descender=e.descender;var b=head.make({flags:3,unitsPerEm:e.unitsPerEm,xMin:x.xMin,yMin:x.yMin,xMax:x.xMax,yMax:x.yMax,lowestRecPPEM:3,createdTimestamp:e.createdTimestamp}),M=hhea.make({ascender:x.ascender,descender:x.descender,advanceWidthMax:x.advanceWidthMax,minLeftSideBearing:x.minLeftSideBearing,minRightSideBearing:x.minRightSideBearing,xMaxExtent:x.maxLeftSideBearing+(x.xMax-x.xMin),numberOfHMetrics:e.glyphs.length}),S=maxp.make(e.glyphs.length),T=os2.make({xAvgCharWidth:Math.round(x.advanceWidthAvg),usWeightClass:e.tables.os2.usWeightClass,usWidthClass:e.tables.os2.usWidthClass,usFirstCharIndex:a,usLastCharIndex:m,ulUnicodeRange1:h,ulUnicodeRange2:o,ulUnicodeRange3:c,ulUnicodeRange4:d,fsSelection:e.tables.os2.fsSelection,sTypoAscender:x.ascender,sTypoDescender:x.descender,sTypoLineGap:0,usWinAscent:x.yMax,usWinDescent:Math.abs(x.yMin),ulCodePageRange1:1,sxHeight:metricsForChar(e,"xyvw",{yMax:Math.round(x.ascender/2)}).yMax,sCapHeight:metricsForChar(e,"HIKLEFJMNTZBDPRAGOQSUVWXY",x).yMax,usDefaultChar:e.hasChar(" ")?32:0,usBreakChar:e.hasChar(" ")?32:0}),k=hmtx.make(e.glyphs),R=cmap.make(e.glyphs),C=e.getEnglishName("fontFamily"),N=e.getEnglishName("fontSubfamily"),E=C+" "+N,O=e.getEnglishName("postScriptName");O||(O=C.replace(/\s/g,"")+"-"+N);var W={};for(var q in e.names)W[q]=e.names[q];W.uniqueID||(W.uniqueID={en:e.getEnglishName("manufacturer")+":"+E}),W.postScriptName||(W.postScriptName={en:O}),W.preferredFamily||(W.preferredFamily=e.names.fontFamily),W.preferredSubfamily||(W.preferredSubfamily=e.names.fontSubfamily);var w=[],B=_name.make(W,w),U=w.length>0?ltag.make(w):void 0,L=post.make(),A=cff.make(e.glyphs,{version:e.getEnglishName("version"),fullName:E,familyName:C,weightName:N,postScriptName:O,unitsPerEm:e.unitsPerEm,fontBBox:[0,x.yMin,x.ascender,x.advanceWidthMax]}),F=e.metas&&Object.keys(e.metas).length>0?meta.make(e.metas):void 0,G=[b,M,S,T,B,R,L,A,k];U&&G.push(U),e.tables.gsub&&G.push(gsub.make(e.tables.gsub)),F&&G.push(F);var D=makeSfntTable(G),H=D.encode(),P=computeCheckSum(H),I=D.fields,j=!1;for(g=0;g<I.length;g+=1)if("head table"===I[g].name){I[g].value.checkSumAdjustment=2981146554-P,j=!0;break}if(!j)throw new Error("Could not find head table with checkSum to adjust.");return D}var check=require("../check"),table=require("../table"),cmap=require("./cmap"),cff=require("./cff"),head=require("./head"),hhea=require("./hhea"),hmtx=require("./hmtx"),ltag=require("./ltag"),maxp=require("./maxp"),_name=require("./name"),os2=require("./os2"),post=require("./post"),gsub=require("./gsub"),meta=require("./meta");exports.computeCheckSum=computeCheckSum,exports.make=makeSfntTable,exports.fontToTable=fontToSfntTable;

},{"../check":2,"../table":13,"./cff":14,"./cmap":15,"./gsub":19,"./head":20,"./hhea":21,"./hmtx":22,"./ltag":25,"./maxp":26,"./meta":27,"./name":28,"./os2":29,"./post":30}],32:[function(require,module,exports){
"use strict";function constant(e){return function(){return e}}var check=require("./check"),LIMIT16=32768,LIMIT32=2147483648,decode={},encode={},sizeOf={};encode.BYTE=function(e){return check.argument(e>=0&&255>=e,"Byte value should be between 0 and 255."),[e]},sizeOf.BYTE=constant(1),encode.CHAR=function(e){return[e.charCodeAt(0)]},sizeOf.CHAR=constant(1),encode.CHARARRAY=function(e){for(var n=[],t=0;t<e.length;t+=1)n[t]=e.charCodeAt(t);return n},sizeOf.CHARARRAY=function(e){return e.length},encode.USHORT=function(e){return[e>>8&255,255&e]},sizeOf.USHORT=constant(2),encode.SHORT=function(e){return e>=LIMIT16&&(e=-(2*LIMIT16-e)),[e>>8&255,255&e]},sizeOf.SHORT=constant(2),encode.UINT24=function(e){return[e>>16&255,e>>8&255,255&e]},sizeOf.UINT24=constant(3),encode.ULONG=function(e){return[e>>24&255,e>>16&255,e>>8&255,255&e]},sizeOf.ULONG=constant(4),encode.LONG=function(e){return e>=LIMIT32&&(e=-(2*LIMIT32-e)),[e>>24&255,e>>16&255,e>>8&255,255&e]},sizeOf.LONG=constant(4),encode.FIXED=encode.ULONG,sizeOf.FIXED=sizeOf.ULONG,encode.FWORD=encode.SHORT,sizeOf.FWORD=sizeOf.SHORT,encode.UFWORD=encode.USHORT,sizeOf.UFWORD=sizeOf.USHORT,encode.LONGDATETIME=function(e){return[0,0,0,0,e>>24&255,e>>16&255,e>>8&255,255&e]},sizeOf.LONGDATETIME=constant(8),encode.TAG=function(e){return check.argument(4===e.length,"Tag should be exactly 4 ASCII characters."),[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]},sizeOf.TAG=constant(4),encode.Card8=encode.BYTE,sizeOf.Card8=sizeOf.BYTE,encode.Card16=encode.USHORT,sizeOf.Card16=sizeOf.USHORT,encode.OffSize=encode.BYTE,sizeOf.OffSize=sizeOf.BYTE,encode.SID=encode.USHORT,sizeOf.SID=sizeOf.USHORT,encode.NUMBER=function(e){return e>=-107&&107>=e?[e+139]:e>=108&&1131>=e?(e-=108,[(e>>8)+247,255&e]):e>=-1131&&-108>=e?(e=-e-108,[(e>>8)+251,255&e]):e>=-32768&&32767>=e?encode.NUMBER16(e):encode.NUMBER32(e)},sizeOf.NUMBER=function(e){return encode.NUMBER(e).length},encode.NUMBER16=function(e){return[28,e>>8&255,255&e]},sizeOf.NUMBER16=constant(3),encode.NUMBER32=function(e){return[29,e>>24&255,e>>16&255,e>>8&255,255&e]},sizeOf.NUMBER32=constant(5),encode.REAL=function(e){var n=e.toString(),t=/\.(\d*?)(?:9{5,20}|0{5,20})\d{0,2}(?:e(.+)|$)/.exec(n);if(t){var o=parseFloat("1e"+((t[2]?+t[2]:0)+t[1].length));n=(Math.round(e*o)/o).toString()}var c,r,i="";for(c=0,r=n.length;r>c;c+=1){var a=n[c];i+="e"===a?"-"===n[++c]?"c":"b":"."===a?"a":"-"===a?"e":a}i+=1&i.length?"f":"ff";var f=[30];for(c=0,r=i.length;r>c;c+=2)f.push(parseInt(i.substr(c,2),16));return f},sizeOf.REAL=function(e){return encode.REAL(e).length},encode.NAME=encode.CHARARRAY,sizeOf.NAME=sizeOf.CHARARRAY,encode.STRING=encode.CHARARRAY,sizeOf.STRING=sizeOf.CHARARRAY,decode.UTF8=function(e,n,t){for(var o=[],c=t,r=0;c>r;r++,n+=1)o[r]=e.getUint8(n);return String.fromCharCode.apply(null,o)},decode.UTF16=function(e,n,t){for(var o=[],c=t/2,r=0;c>r;r++,n+=2)o[r]=e.getUint16(n);return String.fromCharCode.apply(null,o)},encode.UTF16=function(e){for(var n=[],t=0;t<e.length;t+=1){var o=e.charCodeAt(t);n[n.length]=o>>8&255,n[n.length]=255&o}return n},sizeOf.UTF16=function(e){return 2*e.length};var eightBitMacEncodings={"x-mac-croatian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊©⁄€‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ","x-mac-cyrillic":"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю","x-mac-gaelic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØḂ±≤≥ḃĊċḊḋḞḟĠġṀæøṁṖṗɼƒſṠ«»… ÀÃÕŒœ–—“”‘’ṡẛÿŸṪ€‹›Ŷŷṫ·Ỳỳ⁊ÂÊÁËÈÍÎÏÌÓÔ♣ÒÚÛÙıÝýŴŵẄẅẀẁẂẃ","x-mac-greek":"Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦€ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ­","x-mac-icelandic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-inuit":"ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ°ᒡᒥᒦ•¶ᒧ®©™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ… ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł","x-mac-ce":"ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ",macintosh:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-romanian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂȘ∞±≤≥¥µ∂∑∏π∫ªºΩăș¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›Țț‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-turkish":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙˆ˜¯˘˙˚¸˝˛ˇ"};decode.MACSTRING=function(e,n,t,o){var c=eightBitMacEncodings[o];if(void 0===c)return void 0;for(var r="",i=0;t>i;i++){var a=e.getUint8(n+i);r+=127>=a?String.fromCharCode(a):c[127&a]}return r};var macEncodingTableCache="function"==typeof WeakMap&&new WeakMap,macEncodingCacheKeys,getMacEncodingTable=function(e){if(!macEncodingCacheKeys){macEncodingCacheKeys={};for(var n in eightBitMacEncodings)macEncodingCacheKeys[n]=new String(n)}var t=macEncodingCacheKeys[e];if(void 0===t)return void 0;if(macEncodingTableCache){var o=macEncodingTableCache.get(t);if(void 0!==o)return o}var c=eightBitMacEncodings[e];if(void 0===c)return void 0;for(var r={},i=0;i<c.length;i++)r[c.charCodeAt(i)]=i+128;return macEncodingTableCache&&macEncodingTableCache.set(t,r),r};encode.MACSTRING=function(e,n){var t=getMacEncodingTable(n);if(void 0===t)return void 0;for(var o=[],c=0;c<e.length;c++){var r=e.charCodeAt(c);if(r>=128&&(r=t[r],void 0===r))return void 0;o[c]=r}return o},sizeOf.MACSTRING=function(e,n){var t=encode.MACSTRING(e,n);return void 0!==t?t.length:0},encode.INDEX=function(e){var n,t=1,o=[t],c=[];for(n=0;n<e.length;n+=1){var r=encode.OBJECT(e[n]);Array.prototype.push.apply(c,r),t+=r.length,o.push(t)}if(0===c.length)return[0,0];var i=[],a=1+Math.floor(Math.log(t)/Math.log(2))/8|0,f=[void 0,encode.BYTE,encode.USHORT,encode.UINT24,encode.ULONG][a];for(n=0;n<o.length;n+=1){var d=f(o[n]);Array.prototype.push.apply(i,d)}return Array.prototype.concat(encode.Card16(e.length),encode.OffSize(a),i,c)},sizeOf.INDEX=function(e){return encode.INDEX(e).length},encode.DICT=function(e){for(var n=[],t=Object.keys(e),o=t.length,c=0;o>c;c+=1){var r=parseInt(t[c],0),i=e[r];n=n.concat(encode.OPERAND(i.value,i.type)),n=n.concat(encode.OPERATOR(r))}return n},sizeOf.DICT=function(e){return encode.DICT(e).length},encode.OPERATOR=function(e){return 1200>e?[e]:[12,e-1200]},encode.OPERAND=function(e,n){var t=[];if(Array.isArray(n))for(var o=0;o<n.length;o+=1)check.argument(e.length===n.length,"Not enough arguments given for type"+n),t=t.concat(encode.OPERAND(e[o],n[o]));else if("SID"===n)t=t.concat(encode.NUMBER(e));else if("offset"===n)t=t.concat(encode.NUMBER32(e));else if("number"===n)t=t.concat(encode.NUMBER(e));else{if("real"!==n)throw new Error("Unknown operand type "+n);t=t.concat(encode.REAL(e))}return t},encode.OP=encode.BYTE,sizeOf.OP=sizeOf.BYTE;var wmm="function"==typeof WeakMap&&new WeakMap;encode.CHARSTRING=function(e){if(wmm){var n=wmm.get(e);if(void 0!==n)return n}for(var t=[],o=e.length,c=0;o>c;c+=1){var r=e[c];t=t.concat(encode[r.type](r.value))}return wmm&&wmm.set(e,t),t},sizeOf.CHARSTRING=function(e){return encode.CHARSTRING(e).length},encode.OBJECT=function(e){var n=encode[e.type];return check.argument(void 0!==n,"No encoding function for type "+e.type),n(e.value)},sizeOf.OBJECT=function(e){var n=sizeOf[e.type];return check.argument(void 0!==n,"No sizeOf function for type "+e.type),n(e.value)},encode.TABLE=function(e){var n,t=[],o=e.fields.length,c=[],r=[];for(n=0;o>n;n+=1){var i=e.fields[n],a=encode[i.type];check.argument(void 0!==a,"No encoding function for field type "+i.type+" ("+i.name+")");var f=e[i.name];void 0===f&&(f=i.value);var d=a(f);"TABLE"===i.type?(r.push(t.length),t=t.concat([0,0]),c.push(d)):t=t.concat(d)}for(n=0;n<c.length;n+=1){var u=r[n],s=t.length;check.argument(65536>s,"Table "+e.tableName+" too big."),t[u]=s>>8,t[u+1]=255&s,t=t.concat(c[n])}return t},sizeOf.TABLE=function(e){for(var n=0,t=e.fields.length,o=0;t>o;o+=1){var c=e.fields[o],r=sizeOf[c.type];check.argument(void 0!==r,"No sizeOf function for field type "+c.type+" ("+c.name+")");var i=e[c.name];void 0===i&&(i=c.value),n+=r(i),"TABLE"===c.type&&(n+=2)}return n},encode.RECORD=encode.TABLE,sizeOf.RECORD=sizeOf.TABLE,encode.LITERAL=function(e){return e},sizeOf.LITERAL=function(e){return e.length},exports.decode=decode,exports.encode=encode,exports.sizeOf=sizeOf;

},{"./check":2}],33:[function(require,module,exports){
"use strict";exports.isBrowser=function(){return"undefined"!=typeof window},exports.isNode=function(){return"undefined"==typeof window},exports.nodeBufferToArrayBuffer=function(r){for(var e=new ArrayBuffer(r.length),n=new Uint8Array(e),t=0;t<r.length;++t)n[t]=r[t];return e},exports.arrayBufferToNodeBuffer=function(r){for(var e=new Buffer(r.byteLength),n=new Uint8Array(r),t=0;t<e.length;++t)e[t]=n[t];return e},exports.checkArgument=function(r,e){if(!r)throw e};

},{}]},{},[9])(9)
});
export var opentype = window.opentype;
