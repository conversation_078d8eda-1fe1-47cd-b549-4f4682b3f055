/*
 * @Description:
 * @Author: your name
 * @version:
 * @Date: 2024-05-08 16:11:33
 * @LastEditors: your name
 * @LastEditTime: 2024-12-09 11:21:42
 */
import { defineStore } from "pinia";
import * as Cesium from 'cesium'

export const useMapData = defineStore("lineData", {
  state: () => {
    return {
      viewer: null,
      tileset: null
    };
  },
  getters: {
    /**
    * 
    * @returns {Cesium.Viewer}
    */
    Viewer() {
      return this.viewer;
    },
    Tile() {
      return this.tileset;
    }
  },
  actions: {
    /**
     * 
     * @param {Cesium.Viewer} viewer
     * @returns {Cesium.Viewer}
     */
    setViewer(viewer) {
      return new Promise((resolve, reject) => {
        this.viewer = viewer;
        resolve(viewer);
      });
    },
    setTileset(tileset) {
      return new Promise((resolve, reject) => {
        this.tileset = tileset;
        resolve(tileset);
      });
    }
  },
});

