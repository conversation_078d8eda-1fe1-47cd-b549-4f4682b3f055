<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
		<input id="tiffTest" type="button" value="tiff test" />
	</div>

	
	
	<script type="module">

	import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(2_000_000);
		viewer.setMinNodeSize(0);
		viewer.loadSettingsFromURL();
		viewer.setServer("http://localhost:3000");
		
		viewer.setDescription("");
		
		viewer.loadGUI().then(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			// viewer.toggleSidebar();
		});

		let light = new THREE.SpotLight();
		light.distance = 15;
		light.angle = (60 / 180) * Math.PI;
		light.position.set(8.489, 3.577, 5.796);
		light.lookAt(new THREE.Vector3(1.219, -0.171, 2.776));
		viewer.scene.scene.add(light);

		let sph = new Potree.SpotLightHelper(light, new THREE.Color().setHex(0xff0000));
		viewer.scene.scene.add(sph);
		
		// Sigeom
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/archpro/heidentor/cloud.js", "Heidentor", function(e){
			viewer.scene.addPointCloud(e.pointcloud);
			e.pointcloud.position.z = 0;
			let material = e.pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.activeAttributeName = "elevation";
			material.uniforms.uShadowColor.value = [0.6, 0.6, 0.6];

			viewer.scene.view.position.set(19.474, -14.324, 12.829);
			viewer.scene.view.lookAt(0.339, 0.145, 4.073);
		});

		
		Potree.loadPointCloud("../pointclouds/lion_takanawa/cloud.js", "lion", function(e){
			viewer.scene.addPointCloud(e.pointcloud);
			
			let material = e.pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.uniforms.uShadowColor.value = [0.6, 0.6, 0.6];
			
			e.pointcloud.position.set(0, -2, 0);
			
		});

		
	</script>
	
  </body>
</html>
