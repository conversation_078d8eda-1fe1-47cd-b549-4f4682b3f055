<template>
  <div class="floor-plan-container">
    <!-- 指北针 - 移到右上角 -->
    <div class="compass-container">
      <div class="compass">
        <div class="compass-circle">
          <div class="compass-arrow"></div>
          <span class="compass-text">北</span>
        </div>
      </div>
    </div>

    <div class="floor-plan-wrapper">
      <svg
        class="floor-plan-svg"
        viewBox="0 0 800 600"
        xmlns="http://www.w3.org/2000/svg"
      >
        <!-- 外墙 (240mm厚度) -->
        <!-- 外墙外边界 -->
        <rect
          x="40"
          y="40"
          width="720"
          height="520"
          fill="none"
          stroke="#ffffff"
          stroke-width="3"
        />
        <!-- 外墙内边界 -->
        <rect
          x="60"
          y="60"
          width="680"
          height="480"
          fill="none"
          stroke="#ffffff"
          stroke-width="2"
        />
        
        <!-- 外墙填充 -->
        <rect x="40" y="40" width="720" height="20" fill="#555555" stroke="none"/>
        <rect x="40" y="540" width="720" height="20" fill="#555555" stroke="none"/>
        <rect x="40" y="60" width="20" height="480" fill="#555555" stroke="none"/>
        <rect x="740" y="60" width="20" height="480" fill="#555555" stroke="none"/>

        <!-- 内墙 (120mm厚度) -->
        <!-- 客厅与主卧之间的墙 -->
        <rect x="410" y="60" width="10" height="200" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 主卧与次卧/厨房之间的墙 -->
        <rect x="410" y="260" width="200" height="10" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 次卧与厨房之间的墙 -->
        <rect x="610" y="260" width="10" height="160" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 客厅与餐厅/卫生间之间的墙 -->
        <rect x="60" y="360" width="350" height="10" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 卫生间A与过道A之间的墙 -->
        <rect x="60" y="460" width="150" height="10" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 餐厅与卧室B之间的墙 -->
        <rect x="410" y="410" width="10" height="130" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 卧室B与过道B之间的墙 -->
        <rect x="610" y="410" width="10" height="130" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 过道B与卫生间A之间的墙 -->
        <rect x="660" y="410" width="10" height="130" fill="#555555" stroke="#ffffff" stroke-width="1"/>
        
        <!-- 卫生间A与衣帽间之间的墙 -->
        <rect x="670" y="490" width="70" height="10" fill="#555555" stroke="#ffffff" stroke-width="1"/>

        <!-- 客厅 -->
        <rect
          x="60"
          y="60"
          width="350"
          height="300"
          fill="#2c3e50"
          stroke="none"
          class="room living-room"
          @click="goToVR('客厅')"
        />
        <text x="235" y="200" text-anchor="middle" class="room-label">
          客厅
        </text>
        <text x="235" y="220" text-anchor="middle" class="room-area">
          28.1㎡
        </text>

        <!-- 主卧 -->
        <rect
          x="420"
          y="60"
          width="320"
          height="200"
          fill="#34495e"
          stroke="none"
          class="room bedroom"
          @click="goToVR('主卧')"
        />
        <text x="580" y="150" text-anchor="middle" class="room-label">
          卧室A
        </text>
        <text x="580" y="170" text-anchor="middle" class="room-area">
          15.1㎡
        </text>

        <!-- 次卧 -->
        <rect
          x="420"
          y="270"
          width="190"
          height="140"
          fill="#34495e"
          stroke="none"
          class="room bedroom"
          @click="goToVR('次卧')"
        />
        <text x="515" y="335" text-anchor="middle" class="room-label">
          卧室C
        </text>
        <text x="515" y="355" text-anchor="middle" class="room-area">
          12.3㎡
        </text>

        <!-- 厨房 -->
        <rect
          x="620"
          y="270"
          width="120"
          height="140"
          fill="#2c3e50"
          stroke="none"
          class="room kitchen"
          @click="goToVR('厨房')"
        />
        <text x="680" y="335" text-anchor="middle" class="room-label">
          厨房
        </text>
        <text x="680" y="355" text-anchor="middle" class="room-area">
          6.6㎡
        </text>

        <!-- 卫生间A -->
        <rect
          x="60"
          y="370"
          width="150"
          height="90"
          fill="#2c3e50"
          stroke="none"
          class="room bathroom"
          @click="goToVR('卫生间A')"
        />
        <text x="135" y="410" text-anchor="middle" class="room-label">
          卫生间A
        </text>
        <text x="135" y="430" text-anchor="middle" class="room-area">
          2.6㎡
        </text>

        <!-- 过道A -->
        <rect
          x="60"
          y="470"
          width="150"
          height="70"
          fill="#34495e"
          stroke="none"
          class="room corridor"
          @click="goToVR('过道A')"
        />
        <text x="135" y="500" text-anchor="middle" class="room-label">
          过道A
        </text>
        <text x="135" y="520" text-anchor="middle" class="room-area">
          1.6㎡
        </text>

        <!-- 餐厅 -->
        <rect
          x="220"
          y="370"
          width="190"
          height="170"
          fill="#2c3e50"
          stroke="none"
          class="room dining-room"
          @click="goToVR('餐厅')"
        />
        <text x="315" y="450" text-anchor="middle" class="room-label">
          餐厅
        </text>
        <text x="315" y="470" text-anchor="middle" class="room-area">
          11.7㎡
        </text>

        <!-- 卧室B -->
        <rect
          x="420"
          y="420"
          width="190"
          height="120"
          fill="#34495e"
          stroke="none"
          class="room bedroom"
          @click="goToVR('卧室B')"
        />
        <text x="515" y="475" text-anchor="middle" class="room-label">
          卧室B
        </text>
        <text x="515" y="495" text-anchor="middle" class="room-area">
          11.7㎡
        </text>

        <!-- 过道B -->
        <rect
          x="620"
          y="420"
          width="40"
          height="120"
          fill="#34495e"
          stroke="none"
          class="room corridor"
          @click="goToVR('过道B')"
        />
        <text x="640" y="475" text-anchor="middle" class="room-label" style="font-size: 11px;">
          过道B
        </text>
        <text x="640" y="490" text-anchor="middle" class="room-area" style="font-size: 9px;">
          0.9㎡
        </text>

        <!-- 卫生间A -->
        <rect
          x="670"
          y="420"
          width="70"
          height="70"
          fill="#2c3e50"
          stroke="none"
          class="room bathroom"
          @click="goToVR('卫生间A')"
        />
        <text x="705" y="450" text-anchor="middle" class="room-label" style="font-size: 12px;">
          卫生间A
        </text>
        <text x="705" y="465" text-anchor="middle" class="room-area" style="font-size: 10px;">
          4.5㎡
        </text>

        <!-- 衣帽间 -->
        <rect
          x="670"
          y="500"
          width="70"
          height="40"
          fill="#34495e"
          stroke="none"
          class="room closet"
          @click="goToVR('衣帽间')"
        />
        <text x="705" y="520" text-anchor="middle" class="room-label" style="font-size: 11px;">
          衣帽间
        </text>

        <!-- 阳台A -->
        <rect
          x="60"
          y="20"
          width="100"
          height="20"
          fill="#34495e"
          stroke="#ffffff"
          stroke-width="2"
          class="room balcony"
          @click="goToVR('阳台A')"
        />
        <text x="110" y="35" text-anchor="middle" class="room-label" style="font-size: 11px;">
          阳台A 3.2㎡
        </text>

        <!-- 阳台B -->
        <rect
          x="170"
          y="20"
          width="100"
          height="20"
          fill="#34495e"
          stroke="#ffffff"
          stroke-width="2"
          class="room balcony"
          @click="goToVR('阳台B')"
        />
        <text x="220" y="35" text-anchor="middle" class="room-label" style="font-size: 11px;">
          阳台B
        </text>

        <!-- 门的标识 -->
        <line
          x1="385"
          y1="60"
          x2="385"
          y2="90"
          stroke="#ffffff"
          stroke-width="4"
          class="door"
        />
        <line
          x1="220"
          y1="360"
          x2="250"
          y2="360"
          stroke="#ffffff"
          stroke-width="4"
          class="door"
        />
        <line
          x1="420"
          y1="335"
          x2="450"
          y2="335"
          stroke="#ffffff"
          stroke-width="4"
          class="door"
        />
        <line
          x1="620"
          y1="335"
          x2="650"
          y2="335"
          stroke="#ffffff"
          stroke-width="4"
          class="door"
        />
        <line
          x1="135"
          y1="360"
          x2="165"
          y2="360"
          stroke="#ffffff"
          stroke-width="4"
          class="door"
        />
        <line
          x1="585"
          y1="410"
          x2="615"
          y2="410"
          stroke="#ffffff"
          stroke-width="4"
          class="door"
        />

        <!-- 尺寸标注线和数据 -->
        <!-- 顶部水平尺寸线 -->
        <line x1="40" y1="25" x2="760" y2="25" stroke="#ffffff" stroke-width="1" class="dimension-line"/>
        <line x1="40" y1="20" x2="40" y2="30" stroke="#ffffff" stroke-width="1"/>
        <line x1="250" y1="20" x2="250" y2="30" stroke="#ffffff" stroke-width="1"/>
        <line x1="450" y1="20" x2="450" y2="30" stroke="#ffffff" stroke-width="1"/>
        <line x1="760" y1="20" x2="760" y2="30" stroke="#ffffff" stroke-width="1"/>
        
        <text x="145" y="20" text-anchor="middle" class="dimension">2700</text>
        <text x="350" y="20" text-anchor="middle" class="dimension">2662</text>
        <text x="605" y="20" text-anchor="middle" class="dimension">3329</text>
        
        <!-- 左侧垂直尺寸线 -->
        <line x1="25" y1="40" x2="25" y2="560" stroke="#ffffff" stroke-width="1" class="dimension-line"/>
        <line x1="20" y1="40" x2="30" y2="40" stroke="#ffffff" stroke-width="1"/>
        <line x1="20" y1="360" x2="30" y2="360" stroke="#ffffff" stroke-width="1"/>
        <line x1="20" y1="560" x2="30" y2="560" stroke="#ffffff" stroke-width="1"/>
        
        <text x="15" y="200" text-anchor="middle" class="dimension" transform="rotate(-90 15 200)">7500</text>
        <text x="15" y="460" text-anchor="middle" class="dimension" transform="rotate(-90 15 460)">6700</text>
        
        <!-- 底部水平尺寸线 -->
        <line x1="40" y1="575" x2="760" y2="575" stroke="#ffffff" stroke-width="1" class="dimension-line"/>
        <line x1="40" y1="570" x2="40" y2="580" stroke="#ffffff" stroke-width="1"/>
        <line x1="250" y1="570" x2="250" y2="580" stroke="#ffffff" stroke-width="1"/>
        <line x1="450" y1="570" x2="450" y2="580" stroke="#ffffff" stroke-width="1"/>
        <line x1="760" y1="570" x2="760" y2="580" stroke="#ffffff" stroke-width="1"/>
        
        <text x="145" y="590" text-anchor="middle" class="dimension">3000</text>
        <text x="350" y="590" text-anchor="middle" class="dimension">2531</text>
        <text x="605" y="590" text-anchor="middle" class="dimension">3309</text>
      </svg>
    </div>

    <!-- 房间信息面板 -->
    <div v-if="selectedRoom" class="room-info-panel">
      <h3>{{ selectedRoom }}</h3>
      <p>点击进入VR全景浏览</p>
      <button @click="goToVR(selectedRoom)" class="vr-button">
        进入VR浏览
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 当前选中的房间
const selectedRoom = ref('')

// 定义emit用于与父组件通信
const emit = defineEmits(['switchToVR'])

// 点击房间跳转到VR页面
const goToVR = (roomName) => {
  selectedRoom.value = roomName
  console.log(`准备进入${roomName}的VR浏览`)

  // 通过父组件切换到VR页面
  // 由于使用的是动态组件，我们需要通过全局状态或事件来切换
  // 这里我们使用window对象来传递切换信号
  window.dispatchEvent(
    new CustomEvent('switchToVR', {
      detail: { room: roomName },
    })
  )
}

// 监听来自父组件的切换事件
import { onMounted, onUnmounted } from 'vue'

let switchHandler

onMounted(() => {
  // 监听切换到VR的事件
  switchHandler = (event) => {
    // 这里可以添加切换逻辑，但由于使用动态组件，
    // 实际切换需要在App.vue中处理
  }
  window.addEventListener('switchToVR', switchHandler)
})

onUnmounted(() => {
  if (switchHandler) {
    window.removeEventListener('switchToVR', switchHandler)
  }
})
</script>

<style scoped>
.floor-plan-container {
  width: 100%;
  height: 100vh;
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  overflow: auto;
  position: relative;
}

.compass-container {
  position: absolute;
  top: 50px;
  right: 50px;
  z-index: 10;
}

.compass {
  width: 60px;
  height: 60px;
}

.compass-circle {
  width: 100%;
  height: 100%;
  border: 2px solid #ffffff;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(26, 26, 26, 0.8);
}

.compass-arrow {
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 20px solid #ffffff;
}

.compass-arrow::after {
  content: '';
  position: absolute;
  top: 20px;
  left: -3px;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 10px solid #666;
}

.compass-text {
  position: absolute;
  top: -25px;
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 12px;
  color: #ffffff;
  font-weight: normal;
}

.floor-plan-wrapper {
  background: #1a1a1a;
  border-radius: 0;
  padding: 20px;
  max-width: 900px;
  width: 100%;
}

.floor-plan-svg {
  width: 100%;
  height: auto;
  max-height: 600px;
}

.room {
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.9;
}

.room:hover {
  opacity: 1;
  filter: brightness(1.2);
}

.room-label {
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  font-weight: normal;
  fill: #ffffff;
  pointer-events: none;
  user-select: none;
}

.room-area {
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 12px;
  font-weight: normal;
  fill: #cccccc;
  pointer-events: none;
  user-select: none;
}

.dimension {
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 11px;
  fill: #888888;
  pointer-events: none;
  user-select: none;
}

.dimension-line {
  stroke-dasharray: none;
}

.door {
  stroke-linecap: round;
}

.room-info-panel {
  background: #2c2c2c;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  text-align: center;
  min-width: 250px;
  position: absolute;
  bottom: 20px;
}

.room-info-panel h3 {
  margin: 0 0 10px 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: normal;
}

.room-info-panel p {
  margin: 0 0 15px 0;
  color: #cccccc;
}

.vr-button {
  background: #333333;
  color: #ffffff;
  border: 1px solid #555;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.vr-button:hover {
  background: #444444;
  border-color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floor-plan-container {
    padding: 10px;
  }
  
  .compass-container {
    top: 10px;
    right: 10px;
  }
  
  .compass {
    width: 50px;
    height: 50px;
  }
  
  .room-label {
    font-size: 12px;
  }
  
  .room-info-panel {
    position: relative;
    bottom: auto;
    margin-top: 20px;
  }
}
</style>
