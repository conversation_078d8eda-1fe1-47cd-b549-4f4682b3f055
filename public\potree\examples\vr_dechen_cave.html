<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	
	
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">

		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(1_000_000);
		viewer.loadSettingsFromURL();
		viewer.setBackground("skybox");
		
		viewer.setDescription(`Point cloud of <a href="http://www.dechenhoehle.de/" target="_blank">Dechen-Cave, Iserlohn</a>. Courtesy of <a href="http://lrg.geog.uni-heidelberg.de" target="_blank">Heidelberg University, LiDAR Research Group (LRG)</a><br>
			<i style="font-size: small">
			H&auml;mmerle, M., H&ouml;fle, B., Fuchs, J., Schr&ouml;der-Ritzrau, A., Vollweiler, N. & Frank, N. (2014): <a href="http://lrg.geog.uni-heidelberg.de/dechencave/" target="_blank">Comparison of Kinect and Terrestrial LiDAR Capturing Natural Karst Cave 3D Objects</a>. IEEE Geoscience and Remote Sensing Letters, Vol. 11(11), pp 1896-1900, DOI:<a href="http://dx.doi.org/10.1109/LGRS.2014.2313599" target="_blank">10.1109/LGRS.2014.2313599</a>
			</i>`);
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_tools").next().show();
			$("#menu_clipping").next().show();
			viewer.toggleSidebar();
		});
		
		// Load and add point cloud to scene
		Potree.loadPointCloud("https://potree.org/pointclouds/dechen_cave/metadata.json", "Dechen Cave", e => {
			let scene = viewer.scene;
			let pointcloud = e.pointcloud;
			
			let material = pointcloud.material;
			material.size = 0.7;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.shape = Potree.PointShape.SQUARE;
			material.activeAttributeName = "rgba";
			material.rgbGamma = 0.67;
			
			scene.addPointCloud(pointcloud);
			
			// viewer.fitToScreen();
			viewer.scene.view.setView(
				[8.408, 12.242, 2.585],
				[9.270, 12.767, 2.584],
			);
		});

	</script>
	
	
  </body>
</html>
