{"tb": {"navigation_opt": "Navigazione", "rendering_opt": "Visualizzazione", "tools_opt": "Strumenti", "measurments_opt": "Misure", "clipping_opt": "Opzioni di ritaglio", "annotations_opt": "Annotazioni", "materials_opt": "Materiali", "scene_opt": "<PERSON><PERSON>", "classification_filter_opt": "Filtro di classificazione", "filters_opt": "<PERSON><PERSON><PERSON>", "parameters_opt": "Altri parametri", "about_opt": "A proposito"}, "tt": {"angle_measurement": "Misurazione angolo", "point_measurement": "Misurazione punto", "distance_measurement": "Misurazione distanza", "height_measurement": "Misurazione quota", "circle_measurement": "Misurazione cerchio", "area_measurement": "Misurazione superficie", "volume_measurement": "Misurazione volume", "height_profile": "Misurazione profilo", "annotation": "Annotazione", "clip_volume": "Ritaglio con volume", "clip_polygon": "Ritaglio con poligono", "remove_all_measurement": "Cancellazione misure", "left_view_control": "Vista da sinistra", "front_view_control": "Vista frontale", "back_view_control": "Vista da dietro", "top_view_control": "Vista nadirale", "bottom_view_control": "Vista dal basso", "focus_control": "Massima estensione", "orbit_control": "Modalità orbit", "flight_control": "Modalità volo", "heli_control": "Modalità elicottero", "earth_control": "Modalità Earth", "perspective_camera_control": "Vista prospettica", "orthographic_camera_control": "Vista ortografica", "remove_all_clipping_volumes": "Rimuovere tutti i volumi ritagliati", "compass": "Bussola", "camera_animation": "Animazione"}, "appearance": {"nb_max_pts": "Numero di punti", "point_size": "Dimensione dei punti", "min_point_size": "Minima dimensione dei punti", "point_opacity": "Opacità", "field_view": "Angolo di vista", "point_size_type": "Metodo di dimensionamento dei punti", "point_material": "Materiale", "elevation_range": "Intervallo altimetrico", "extra_range": "Intervallo scalare", "point_quality": "Qualità", "point_shape": "Forma", "edl_radius": "Raggio", "edl_strength": "Intensità", "edl_opacity": "Opacità", "edl_enable": "<PERSON><PERSON><PERSON>", "min_node_size": "Min dimensione dei nodi", "clip_mode": "Modalità di ritaglio", "move_speed": "Velocità", "skybox": "Cielo", "bottom_lock": "<PERSON><PERSON><PERSON> sotto al suolo", "box": "Box", "length_unit": "Unità di misura", "freeze": "Blocca vista", "language": "<PERSON><PERSON>"}, "measurements": {"clip": "ritaglia", "show": "mostra volume"}, "annotations": {"show3D": "mostra in 3D", "showMap": "mostra sulla mappa"}, "profile": {"nb_points": "Numero di punti", "title": "<PERSON><PERSON>", "save_las": "<PERSON>va come LAS(3D)", "save_ortho": "Salva come CSV(2D)"}, "scene": {"camera_position": "Posizione della camera", "camera_target": "Obiettivo della camera"}, "filters": {"return_number": "Numero di ritorno", "number_of_returns": "Numero di ritorno", "gps_min": "min", "gps_max": "max", "gps_time": "tempo GPS"}, "settings": {"language": "<PERSON><PERSON>"}}