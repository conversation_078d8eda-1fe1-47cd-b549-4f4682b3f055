INFO(chunker_countsort_laszip.cpp:176): counting erciyouhua.las, first point: 0, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting erciyouhua.las, first point: 2'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting erciyouhua.las, first point: 4'000'000, num points: 268'187
INFO(chunker_countsort_laszip.cpp:176): counting erciyouhua.las, first point: 1'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting erciyouhua.las, first point: 3'000'000, num points: 1'000'000
INFO(indexer.cpp:1656): start indexing chunk r20
filesize: 635'285
min: -14.644, -6.3130000000000006, -1.5230000000000001
max: -7.6514999999999995, 0.67949999999999999, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r241
filesize: 5'180
min: -7.6514999999999995, -6.3130000000000006, 1.9732499999999997
max: -4.1552499999999988, -2.8167500000000003, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r22
filesize: 204'750
min: -14.644, 0.67949999999999999, -1.5230000000000001
max: -7.6514999999999995, 7.6720000000000006, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r240
filesize: 4'053'245
min: -7.6514999999999995, -6.3130000000000006, -1.5230000000000001
max: -4.1552499999999988, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2441
filesize: 1'771'770
min: -4.1552499999999988, -6.3130000000000006, 0.2251249999999998
max: -2.4071249999999988, -4.5648750000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r242
filesize: 4'432'400
min: -7.6514999999999995, -2.8167500000000003, -1.5230000000000001
max: -4.1552499999999988, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r243
filesize: 52'850
min: -7.6514999999999995, -2.8167500000000003, 1.9732499999999997
max: -4.1552499999999988, 0.67949999999999999, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r2440
filesize: 665'665
min: -4.1552499999999988, -6.3130000000000006, -1.5230000000000001
max: -2.4071249999999988, -4.5648750000000007, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2442
filesize: 812'560
min: -4.1552499999999988, -4.5648750000000007, -1.5230000000000001
max: -2.4071249999999988, -2.8167500000000003, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2443
filesize: 886'655
min: -4.1552499999999988, -4.5648750000000007, 0.2251249999999998
max: -2.4071249999999988, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2444
filesize: 951'650
min: -2.4071249999999988, -6.3130000000000006, -1.5230000000000001
max: -0.65899999999999892, -4.5648750000000007, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2445
filesize: 2'198'350
min: -2.4071249999999988, -6.3130000000000006, 0.2251249999999998
max: -0.65899999999999892, -4.5648750000000007, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r241
INFO(indexer.cpp:1656): start indexing chunk r2446
filesize: 1'077'615
min: -2.4071249999999988, -4.5648750000000007, -1.5230000000000001
max: -0.65899999999999892, -2.8167500000000003, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r22
INFO(indexer.cpp:1699): finished indexing chunk r243
INFO(indexer.cpp:1656): start indexing chunk r2447
filesize: 1'828'855
min: -2.4071249999999988, -4.5648750000000007, 0.2251249999999998
max: -0.65899999999999892, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2465
filesize: 2'910'635
min: -2.4071249999999988, -2.8167500000000003, 0.2251249999999998
max: -0.65899999999999892, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2461
filesize: 903'945
min: -4.1552499999999988, -2.8167500000000003, 0.2251249999999998
max: -2.4071249999999988, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2466
filesize: 1'130'535
min: -2.4071249999999988, -1.0686250000000002, -1.5230000000000001
max: -0.65899999999999892, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r247
filesize: 140
min: -4.1552499999999988, -2.8167500000000003, 1.9732499999999997
max: -0.65899999999999892, 0.67949999999999999, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r2462
filesize: 719'355
min: -4.1552499999999988, -1.0686250000000002, -1.5230000000000001
max: -2.4071249999999988, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2460
filesize: 574'385
min: -4.1552499999999988, -2.8167500000000003, -1.5230000000000001
max: -2.4071249999999988, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r247
INFO(indexer.cpp:1656): start indexing chunk r260
filesize: 2'262'785
min: -7.6514999999999995, 0.67949999999999999, -1.5230000000000001
max: -4.1552499999999988, 4.1757500000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2464
filesize: 1'465'660
min: -2.4071249999999988, -2.8167500000000003, -1.5230000000000001
max: -0.65899999999999892, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2463
filesize: 1'047'865
min: -4.1552499999999988, -1.0686250000000002, 0.2251249999999998
max: -2.4071249999999988, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2467
filesize: 2'344'090
min: -2.4071249999999988, -1.0686250000000002, 0.2251249999999998
max: -0.65899999999999892, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r2640
filesize: 967'960
min: -4.1552499999999988, 0.67949999999999999, -1.5230000000000001
max: -2.4071249999999988, 2.4276250000000004, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r261
filesize: 3'885
min: -7.6514999999999995, 0.67949999999999999, 1.9732499999999997
max: -4.1552499999999988, 4.1757500000000007, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r2641
filesize: 1'671'250
min: -4.1552499999999988, 0.67949999999999999, 0.2251249999999998
max: -2.4071249999999988, 2.4276250000000004, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r261
INFO(indexer.cpp:1656): start indexing chunk r2642
filesize: 544'915
min: -4.1552499999999988, 2.4276250000000004, -1.5230000000000001
max: -2.4071249999999988, 4.1757500000000007, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2643
filesize: 1'115'205
min: -4.1552499999999988, 2.4276250000000004, 0.2251249999999998
max: -2.4071249999999988, 4.1757500000000007, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2440
INFO(indexer.cpp:1699): finished indexing chunk r20
INFO(indexer.cpp:1656): start indexing chunk r2644
filesize: 1'206'975
min: -2.4071249999999988, 0.67949999999999999, -1.5230000000000001
max: -0.65899999999999892, 2.4276250000000004, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r2645
filesize: 2'352'315
min: -2.4071249999999988, 0.67949999999999999, 0.2251249999999998
max: -0.65899999999999892, 2.4276250000000004, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2443
INFO(indexer.cpp:1699): finished indexing chunk r2446
INFO(indexer.cpp:1656): start indexing chunk r2646
filesize: 466'620
min: -2.4071249999999988, 2.4276250000000004, -1.5230000000000001
max: -0.65899999999999892, 4.1757500000000007, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r2646
INFO(indexer.cpp:1656): start indexing chunk r2647
filesize: 1'075'760
min: -2.4071249999999988, 2.4276250000000004, 0.2251249999999998
max: -0.65899999999999892, 4.1757500000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r265
filesize: 17'430
min: -4.1552499999999988, 0.67949999999999999, 1.9732499999999997
max: -0.65899999999999892, 4.1757500000000007, 5.4695
INFO(indexer.cpp:1699): finished indexing chunk r265
INFO(indexer.cpp:1699): finished indexing chunk r2442
INFO(indexer.cpp:1699): finished indexing chunk r2642
INFO(indexer.cpp:1656): start indexing chunk r266
filesize: 16'660
min: -4.1552499999999988, 4.1757500000000007, -1.5230000000000001
max: -0.65899999999999892, 7.6720000000000006, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r266
INFO(indexer.cpp:1699): finished indexing chunk r2460
INFO(indexer.cpp:1656): start indexing chunk r40
filesize: 41'405
min: -0.65899999999999892, -20.298000000000002, -1.5230000000000001
max: 6.3335000000000017, -13.305500000000002, 5.4695
INFO(indexer.cpp:1699): finished indexing chunk r2444
INFO(indexer.cpp:1699): finished indexing chunk r40
INFO(indexer.cpp:1656): start indexing chunk r420
filesize: 175'910
min: -0.65899999999999892, -13.305500000000002, -1.5230000000000001
max: 2.8372500000000014, -9.8092500000000022, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2647
INFO(indexer.cpp:1699): finished indexing chunk r420
INFO(indexer.cpp:1656): start indexing chunk r422
filesize: 2'592'240
min: -0.65899999999999892, -9.8092500000000022, -1.5230000000000001
max: 2.8372500000000014, -6.3130000000000006, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2644
INFO(indexer.cpp:1656): start indexing chunk r424
filesize: 1'728'090
min: 2.8372500000000014, -13.305500000000002, -1.5230000000000001
max: 6.3335000000000017, -9.8092500000000022, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2463
INFO(indexer.cpp:1699): finished indexing chunk r2466
INFO(indexer.cpp:1656): start indexing chunk r4262
filesize: 1'198'715
min: 2.8372500000000014, -8.0611250000000005, -1.5230000000000001
max: 4.5853750000000018, -6.3130000000000006, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r4260
filesize: 547'960
min: 2.8372500000000014, -9.8092500000000022, -1.5230000000000001
max: 4.5853750000000018, -8.0611250000000005, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r4261
filesize: 1'325'905
min: 2.8372500000000014, -9.8092500000000022, 0.2251249999999998
max: 4.5853750000000018, -8.0611250000000005, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2462
INFO(indexer.cpp:1656): start indexing chunk r4263
filesize: 1'804'600
min: 2.8372500000000014, -8.0611250000000005, 0.2251249999999998
max: 4.5853750000000018, -6.3130000000000006, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2441
INFO(indexer.cpp:1656): start indexing chunk r4264
filesize: 663'670
min: 4.5853750000000018, -9.8092500000000022, -1.5230000000000001
max: 6.3335000000000017, -8.0611250000000005, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r2640
INFO(indexer.cpp:1656): start indexing chunk r4265
filesize: 840'665
min: 4.5853750000000018, -9.8092500000000022, 0.2251249999999998
max: 6.3335000000000017, -8.0611250000000005, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2461
INFO(indexer.cpp:1656): start indexing chunk r4266
filesize: 958'405
min: 4.5853750000000018, -8.0611250000000005, -1.5230000000000001
max: 6.3335000000000017, -6.3130000000000006, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r4260
INFO(indexer.cpp:1656): start indexing chunk r460
filesize: 1'556'625
min: 6.3335000000000017, -13.305500000000002, -1.5230000000000001
max: 9.8297500000000024, -9.8092500000000022, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r4267
filesize: 1'714'790
min: 4.5853750000000018, -8.0611250000000005, 0.2251249999999998
max: 6.3335000000000017, -6.3130000000000006, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r2643
INFO(indexer.cpp:1656): start indexing chunk r462
filesize: 6'895'455
min: 6.3335000000000017, -9.8092500000000022, -1.5230000000000001
max: 9.8297500000000024, -6.3130000000000006, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6000
filesize: 1'063'195
min: -0.65899999999999892, -6.3130000000000006, -1.5230000000000001
max: 1.0891250000000012, -4.5648750000000007, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r4264
INFO(indexer.cpp:1699): finished indexing chunk r2464
INFO(indexer.cpp:1699): finished indexing chunk r4262
INFO(indexer.cpp:1656): start indexing chunk r6001
filesize: 1'671'390
min: -0.65899999999999892, -6.3130000000000006, 0.2251249999999998
max: 1.0891250000000012, -4.5648750000000007, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r4265
INFO(indexer.cpp:1656): start indexing chunk r6002
filesize: 1'006'635
min: -0.65899999999999892, -4.5648750000000007, -1.5230000000000001
max: 1.0891250000000012, -2.8167500000000003, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r2641
INFO(indexer.cpp:1656): start indexing chunk r6003
filesize: 1'665'615
min: -0.65899999999999892, -4.5648750000000007, 0.2251249999999998
max: 1.0891250000000012, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r4266
INFO(indexer.cpp:1699): finished indexing chunk r4261
INFO(indexer.cpp:1656): start indexing chunk r6005
filesize: 2'160'760
min: 1.0891250000000012, -6.3130000000000006, 0.2251249999999998
max: 2.8372500000000014, -4.5648750000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6004
filesize: 1'395'135
min: 1.0891250000000012, -6.3130000000000006, -1.5230000000000001
max: 2.8372500000000014, -4.5648750000000007, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6006
filesize: 1'168'790
min: 1.0891250000000012, -4.5648750000000007, -1.5230000000000001
max: 2.8372500000000014, -2.8167500000000003, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6000
INFO(indexer.cpp:1656): start indexing chunk r6007
filesize: 1'251'670
min: 1.0891250000000012, -4.5648750000000007, 0.2251249999999998
max: 2.8372500000000014, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6020
filesize: 961'975
min: -0.65899999999999892, -2.8167500000000003, -1.5230000000000001
max: 1.0891250000000012, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r2445
INFO(indexer.cpp:1699): finished indexing chunk r424
INFO(indexer.cpp:1656): start indexing chunk r6021
filesize: 2'398'340
min: -0.65899999999999892, -2.8167500000000003, 0.2251249999999998
max: 1.0891250000000012, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6002
INFO(indexer.cpp:1699): finished indexing chunk r4263
INFO(indexer.cpp:1699): finished indexing chunk r2645
INFO(indexer.cpp:1656): start indexing chunk r6022
filesize: 611'835
min: -0.65899999999999892, -1.0686250000000002, -1.5230000000000001
max: 1.0891250000000012, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r422
INFO(indexer.cpp:1699): finished indexing chunk r2447
INFO(indexer.cpp:1656): start indexing chunk r6023
filesize: 1'035'755
min: -0.65899999999999892, -1.0686250000000002, 0.2251249999999998
max: 1.0891250000000012, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6024
filesize: 1'266'965
min: 1.0891250000000012, -2.8167500000000003, -1.5230000000000001
max: 2.8372500000000014, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r460
INFO(indexer.cpp:1699): finished indexing chunk r6020
INFO(indexer.cpp:1656): start indexing chunk r6025
filesize: 3'240'265
min: 1.0891250000000012, -2.8167500000000003, 0.2251249999999998
max: 2.8372500000000014, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6027
filesize: 2'311'015
min: 1.0891250000000012, -1.0686250000000002, 0.2251249999999998
max: 2.8372500000000014, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6026
filesize: 1'165'780
min: 1.0891250000000012, -1.0686250000000002, -1.5230000000000001
max: 2.8372500000000014, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r260
INFO(indexer.cpp:1656): start indexing chunk r6040
filesize: 1'528'135
min: 2.8372500000000014, -6.3130000000000006, -1.5230000000000001
max: 4.5853750000000018, -4.5648750000000007, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r4267
INFO(indexer.cpp:1699): finished indexing chunk r6022
INFO(indexer.cpp:1656): start indexing chunk r6041
filesize: 2'177'595
min: 2.8372500000000014, -6.3130000000000006, 0.2251249999999998
max: 4.5853750000000018, -4.5648750000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6042
filesize: 1'008'210
min: 2.8372500000000014, -4.5648750000000007, -1.5230000000000001
max: 4.5853750000000018, -2.8167500000000003, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6006
INFO(indexer.cpp:1656): start indexing chunk r6043
filesize: 1'155'525
min: 2.8372500000000014, -4.5648750000000007, 0.2251249999999998
max: 4.5853750000000018, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6044
filesize: 882'735
min: 4.5853750000000018, -6.3130000000000006, -1.5230000000000001
max: 6.3335000000000017, -4.5648750000000007, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6045
filesize: 1'365'840
min: 4.5853750000000018, -6.3130000000000006, 0.2251249999999998
max: 6.3335000000000017, -4.5648750000000007, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6007
INFO(indexer.cpp:1699): finished indexing chunk r6004
INFO(indexer.cpp:1656): start indexing chunk r6046
filesize: 1'056'090
min: 4.5853750000000018, -4.5648750000000007, -1.5230000000000001
max: 6.3335000000000017, -2.8167500000000003, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r2467
INFO(indexer.cpp:1699): finished indexing chunk r6001
INFO(indexer.cpp:1656): start indexing chunk r6047
filesize: 1'285'760
min: 4.5853750000000018, -4.5648750000000007, 0.2251249999999998
max: 6.3335000000000017, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6060
filesize: 922'075
min: 2.8372500000000014, -2.8167500000000003, -1.5230000000000001
max: 4.5853750000000018, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6023
INFO(indexer.cpp:1656): start indexing chunk r6061
filesize: 2'350'600
min: 2.8372500000000014, -2.8167500000000003, 0.2251249999999998
max: 4.5853750000000018, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6062
filesize: 296'170
min: 2.8372500000000014, -1.0686250000000002, -1.5230000000000001
max: 4.5853750000000018, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6042
INFO(indexer.cpp:1699): finished indexing chunk r6062
INFO(indexer.cpp:1699): finished indexing chunk r6026
INFO(indexer.cpp:1656): start indexing chunk r6063
filesize: 923'825
min: 2.8372500000000014, -1.0686250000000002, 0.2251249999999998
max: 4.5853750000000018, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6044
INFO(indexer.cpp:1656): start indexing chunk r6064
filesize: 1'306'795
min: 4.5853750000000018, -2.8167500000000003, -1.5230000000000001
max: 6.3335000000000017, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6065
filesize: 2'661'260
min: 4.5853750000000018, -2.8167500000000003, 0.2251249999999998
max: 6.3335000000000017, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6066
filesize: 1'642'025
min: 4.5853750000000018, -1.0686250000000002, -1.5230000000000001
max: 6.3335000000000017, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6067
filesize: 3'150'490
min: 4.5853750000000018, -1.0686250000000002, 0.2251249999999998
max: 6.3335000000000017, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6043
INFO(indexer.cpp:1699): finished indexing chunk r6046
INFO(indexer.cpp:1699): finished indexing chunk r6003
INFO(indexer.cpp:1699): finished indexing chunk r6060
INFO(indexer.cpp:1656): start indexing chunk r6200
filesize: 500'640
min: -0.65899999999999892, 0.67949999999999999, -1.5230000000000001
max: 1.0891250000000012, 2.4276250000000004, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6024
INFO(indexer.cpp:1699): finished indexing chunk r6040
INFO(indexer.cpp:1699): finished indexing chunk r242
INFO(indexer.cpp:1656): start indexing chunk r6202
filesize: 627'515
min: -0.65899999999999892, 2.4276250000000004, -1.5230000000000001
max: 1.0891250000000012, 4.1757500000000007, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6201
filesize: 1'101'975
min: -0.65899999999999892, 0.67949999999999999, 0.2251249999999998
max: 1.0891250000000012, 2.4276250000000004, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6063
INFO(indexer.cpp:1656): start indexing chunk r6203
filesize: 1'382'570
min: -0.65899999999999892, 2.4276250000000004, 0.2251249999999998
max: 1.0891250000000012, 4.1757500000000007, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6200
INFO(indexer.cpp:1699): finished indexing chunk r6047
INFO(indexer.cpp:1699): finished indexing chunk r6045
INFO(indexer.cpp:1699): finished indexing chunk r2465
INFO(indexer.cpp:1656): start indexing chunk r6204
filesize: 1'039'255
min: 1.0891250000000012, 0.67949999999999999, -1.5230000000000001
max: 2.8372500000000014, 2.4276250000000004, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6205
filesize: 2'257'150
min: 1.0891250000000012, 0.67949999999999999, 0.2251249999999998
max: 2.8372500000000014, 2.4276250000000004, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6206
filesize: 434'805
min: 1.0891250000000012, 2.4276250000000004, -1.5230000000000001
max: 2.8372500000000014, 4.1757500000000007, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6202
INFO(indexer.cpp:1656): start indexing chunk r6207
filesize: 767'235
min: 1.0891250000000012, 2.4276250000000004, 0.2251249999999998
max: 2.8372500000000014, 4.1757500000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r621
filesize: 1'855
min: -0.65899999999999892, 0.67949999999999999, 1.9732499999999997
max: 2.8372500000000014, 4.1757500000000007, 5.4695
INFO(indexer.cpp:1656): start indexing chunk r622
filesize: 87'990
min: -0.65899999999999892, 4.1757500000000007, -1.5230000000000001
max: 2.8372500000000014, 7.6720000000000006, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r621
INFO(indexer.cpp:1699): finished indexing chunk r622
INFO(indexer.cpp:1656): start indexing chunk r624
filesize: 5'117'350
min: 2.8372500000000014, 0.67949999999999999, -1.5230000000000001
max: 6.3335000000000017, 4.1757500000000007, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r626
filesize: 2'485
min: 2.8372500000000014, 4.1757500000000007, -1.5230000000000001
max: 6.3335000000000017, 7.6720000000000006, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r626
INFO(indexer.cpp:1699): finished indexing chunk r6005
INFO(indexer.cpp:1656): start indexing chunk r640
filesize: 7'206'500
min: 6.3335000000000017, -6.3130000000000006, -1.5230000000000001
max: 9.8297500000000024, -2.8167500000000003, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6420
filesize: 1'052'870
min: 6.3335000000000017, -2.8167500000000003, -1.5230000000000001
max: 8.0816250000000025, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6421
filesize: 1'116'745
min: 6.3335000000000017, -2.8167500000000003, 0.2251249999999998
max: 8.0816250000000025, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1699): finished indexing chunk r6206
INFO(indexer.cpp:1699): finished indexing chunk r6064
INFO(indexer.cpp:1699): finished indexing chunk r6201
INFO(indexer.cpp:1699): finished indexing chunk r6207
INFO(indexer.cpp:1656): start indexing chunk r6422
filesize: 1'483'895
min: 6.3335000000000017, -1.0686250000000002, -1.5230000000000001
max: 8.0816250000000025, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6423
filesize: 1'521'870
min: 6.3335000000000017, -1.0686250000000002, 0.2251249999999998
max: 8.0816250000000025, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6424
filesize: 517'580
min: 8.0816250000000025, -2.8167500000000003, -1.5230000000000001
max: 9.8297500000000024, -1.0686250000000002, 0.2251249999999998
INFO(indexer.cpp:1656): start indexing chunk r6425
filesize: 1'426'985
min: 8.0816250000000025, -2.8167500000000003, 0.2251249999999998
max: 9.8297500000000024, -1.0686250000000002, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r6426
filesize: 975'205
min: 8.0816250000000025, -1.0686250000000002, -1.5230000000000001
max: 9.8297500000000024, 0.67949999999999999, 0.2251249999999998
INFO(indexer.cpp:1699): finished indexing chunk r6021
INFO(indexer.cpp:1699): finished indexing chunk r6204
INFO(indexer.cpp:1699): finished indexing chunk r240
INFO(indexer.cpp:1656): start indexing chunk r6427
filesize: 1'585'080
min: 8.0816250000000025, -1.0686250000000002, 0.2251249999999998
max: 9.8297500000000024, 0.67949999999999999, 1.9732499999999997
INFO(indexer.cpp:1656): start indexing chunk r66
filesize: 6'671'105
min: 6.3335000000000017, 0.67949999999999999, -1.5230000000000001
max: 13.326000000000002, 7.6720000000000006, 5.4695
INFO(indexer.cpp:1699): finished indexing chunk r6424
INFO(indexer.cpp:1699): finished indexing chunk r6421
INFO(indexer.cpp:1699): finished indexing chunk r6027
INFO(indexer.cpp:1699): finished indexing chunk r6420
INFO(indexer.cpp:1699): finished indexing chunk r6425
INFO(indexer.cpp:1699): finished indexing chunk r6041
INFO(indexer.cpp:1699): finished indexing chunk r6203
INFO(indexer.cpp:1699): finished indexing chunk r6066
INFO(indexer.cpp:1699): finished indexing chunk r6426
INFO(indexer.cpp:1699): finished indexing chunk r6427
INFO(indexer.cpp:1699): finished indexing chunk r6423
INFO(indexer.cpp:1699): finished indexing chunk r6422
INFO(indexer.cpp:1699): finished indexing chunk r6205
INFO(indexer.cpp:1699): finished indexing chunk r6061
INFO(indexer.cpp:1699): finished indexing chunk r6065
INFO(indexer.cpp:1699): finished indexing chunk r6025
INFO(indexer.cpp:1699): finished indexing chunk r462
INFO(indexer.cpp:1699): finished indexing chunk r6067
INFO(indexer.cpp:1699): finished indexing chunk r624
INFO(indexer.cpp:1699): finished indexing chunk r66
INFO(indexer.cpp:1699): finished indexing chunk r640
