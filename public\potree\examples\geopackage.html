<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">

	import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(5_000_000);
		viewer.loadSettingsFromURL();
		
		viewer.setDescription("");
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			//$("#menu_appearance").next().show();
			$("#menu_tools").next().show();
			$("#menu_scene").next().show();
			$("#menu_filters").next().show();
			viewer.toggleSidebar();
		});
		
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/opentopography/CA13_1.4/cloud.js", "CA13", async function(e){
			viewer.scene.addPointCloud(e.pointcloud);
			let material = e.pointcloud.material;
			material.size = 0.7;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.activeAttributeName = "rgba";
			
			viewer.scene.view.setView(
				[693943.373, 3915593.308, 1051.287],
				[694667.183, 3916308.972, -96.390],
			);

			{ // load a geopackage
				proj4.defs("WGS84", "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs");
				proj4.defs("pointcloud", viewer.getProjection());
				const params = {
					transform: proj4("WGS84", "pointcloud"),
				};

				const url = "./morro_bay_shp/gpkg/geopackage.gpkg";
				const geopackage = await Potree.GeoPackageLoader.loadUrl(url, params);
				viewer.scene.addGeopackage(geopackage);
			}
		});
		
	</script>
	
	
  </body>
</html>
