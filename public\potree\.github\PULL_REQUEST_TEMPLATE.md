
Please follow these instructions when creating a new pull request:

* Validating PRs can take a lot of time. The simpler the PR, the higher the chance that it will get accepted. 
* Set the potree develop branch as the PR target.
* Builds should not be part of the PR. Don't commit them. 
* Do not use a formatter. They make a mess out of diffs. 
* It takes a lot of time to download and test, as well as analyze the diffs,
so please excuse if it takes time to respond or if you don't get a response. 





