<template>
  <div id="cesium-viewer">
    <slot />
  </div>
</template>

<script setup>
import * as Cesium from 'cesium'
import { onMounted, markRaw } from 'vue'
import { useMapData } from '../../../store/index'
import {
  initViewer,
  setScene,
  loadTilesets,
} from '../../../cesiumTools/sceneManager.js'

//初始化cesium实例
Cesium.Ion.defaultAccessToken =
   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI3ZDE2MWZmOC1hN2Y0LTQ3MjMtOGEyZi0yOTkxMmY5ZDlmMTIiLCJpZCI6MjU4MzQxLCJpYXQiOjE3MzI2OTMxMDl9.gbx9s_BWEUa4ExU53SpskEdoNSg7J7ENI2pOpE8L0Vg'

const dataStore = useMapData()
onMounted(async () => {
  const viewer = initViewer('cesium-viewer')
  setScene(viewer)
  // 可以使用pinia来管理viewer地图对象，只需要将其标记为非响应式即可
  dataStore.setViewer(markRaw(viewer))
  const modelUrls = [
    {
      url: 'http://localhost:8888/model/tileset.json',
      options: {},
    },
  ]
  // 加载多个3dtiles
  await loadTilesets(viewer, modelUrls, (tilesets) => {
    dataStore.setTileset(tilesets[0])
    viewer.zoomTo(tilesets[0])
  })
})
</script>
<style scoped>
.cesium-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background-color: #000;
  z-index: 1;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
  pointer-events: all;
  position: absolute;
  top: 0;
  left: 0;
}

.debug-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.8);
  padding: 5px 10px;
  border-radius: 3px;
  z-index: 1000;
  font-size: 12px;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 5px;
  z-index: 1000;
}

.loading-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 5px;
  z-index: 1000;
}
</style>
