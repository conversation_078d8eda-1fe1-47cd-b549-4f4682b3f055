<!--
 * @Description: 
 * @Author: your name
 * @version: 
 * @Date: 2024-12-09 09:52:23
 * @LastEditors: your name
 * @LastEditTime: 2024-12-09 14:45:42
-->
<template>
  <a-card title="楼房分户" style="width: 600px;pointer-events: all;">
    <template #extra>
      <UndoOutlined @click="refresh" />
    </template>
    <a-steps :current="currentStep" :items="steps">
    </a-steps>
    <div class="draw-area">
      <span>绘制户型:</span>
      <ForkOutlined class="icon-draw" @click="activeDraw" />
      <span>{{ textDraw }}</span>
    </div>
    <div class="form-area">
      <a-table size="small" bordered :data-source="buildingData" :columns="buildingColumns" :pagination="false">
        <template #bodyCell="{ column, text, record }">
          <template v-if="['posPrefix', 'unit'].includes(column.dataIndex)">
            <div>
              <a-input v-if="editableData[record.key]" v-model:value="editableData[record.key][column.dataIndex]"
                style="margin: -5px 0" />
              <template v-else>
                {{ text }}
              </template>
            </div>
          </template>

          <!-- 只有做区域绘制的时候，需要编辑 -->
          <template v-else-if="column.dataIndex === 'operation' && currentStep !== 2">
            <div class="editable-row-operations">
              <span v-if="editableData[record.key]" style="display: flex;justify-content: space-around;">
                <a-typography-link @click="save(record.key)">保存</a-typography-link>
                <a-popconfirm title="Sure to cancel?" @confirm="cancel(record.key)">
                  <a>取消</a>
                </a-popconfirm>
              </span>
              <span v-else>
                <a @click="edit(record.key)">编辑</a>
              </span>
            </div>
          </template>

          <!-- 只有户型切分的时候，需要定位 -->
          <template v-if="['positionFix'].includes(column.dataIndex)">
            <div @click="focusOnArea(record.key)">
              <img style="cursor: pointer;" width="20" height="20" src="/src/assets/position.png" alt="">
            </div>
          </template>
        </template>

      </a-table>
    </div>
    <!-- 楼房分层 -->
    <div class="floor-area" v-if="currentStep === 2 && showDivide">
      <span>最低点:</span>
      <a-input width="100" :value="minHeight" :min="-100" :max="200" :disabled="true"></a-input>
      <span>分割点:</span>
      <a-input width="100" :value="divideHeight" :min="-100" :max="200" :disabled="true"></a-input>
      <span>最高点:</span>
      <a-input width="100" :value="maxHeight" :min="-100" :max="200" :disabled="true"></a-input>
      <span>楼层数:</span>
      <a-input width="100" v-model:value="floorNum" :min="0" :max="100"></a-input>
    </div>
    <div class="button-area">
      <a-button v-if="currentStep < steps.length - 1" type="primary" @click="nextStep">下一步</a-button>
      <a-button v-if="showDivide" @click="divideFloor" style="margin:0 10px;">楼房分层</a-button>
      <a-button v-if="currentStep == steps.length - 1 && updateActive" type="primary" @click="updateInfo">
        生成数据
      </a-button>
    </div>
  </a-card>
</template>

<script setup>
import * as Cesium from 'cesium'
import { ref, reactive, computed, watch, onBeforeUnmount } from 'vue';
import { cloneDeep } from 'lodash'
import { binkEntityMaterial } from '../cesiumTools/sceneManager.js'
import { changeCartesin3ToGeoJson, polygonCut, cartesian3ToDegreesHeight } from '../cesiumTools/utils'
import DrawTool from '../cesiumTools/drawTool'

import { useMapData } from '../store'
import { addHouse } from '../api/api'
import { message } from 'ant-design-vue';

const mapStore = useMapData()
const viewer = mapStore.Viewer
const drawTool = new DrawTool(viewer)
const currentStep = ref(0)
const showDivide = ref(false)
const updateActive = ref(false)
const minHeight = ref(0)
const divideHeight = ref(0)
const maxHeight = ref(0)
const floorNum = ref(0)

// 推送到服务端的数据 addHouse
// polygonJson总区域geojson字符串
// polygonJsonArr切割后geojson字符串数组，如果没有切割，传[]
// unitArr 单位数组
// heightArr 三个高度数组
// name 楼栋名称
// floorNum楼层数
const postDataSource = {
  polygonJson: '',
  polygonJsonArr: [],
  unitArr: [],
  heightArr: [],
  name: '',
  floorNum: 0
}
// 表格数据
const buildingData = ref([])
const buildingColumns = ref([{
  title: '分户坐标',
  dataIndex: 'position',
  align: 'center',
  width: '30%',
  ellipsis: true
}, {
  title: '地址前缀',
  dataIndex: 'posPrefix',
  align: 'center',
  width: '30%',
  ellipsis: true
}, {
  title: '单位',
  dataIndex: 'unit',
  align: 'center'
}, {
  title: '操作',
  dataIndex: 'operation',
  align: 'center'
}])

const steps = ref([
  {
    title: '区域绘制',
    key: '区域绘制'
  }, {
    title: '户型切分',
    key: '户型切分'
  }, {
    title: '楼房分层',
    key: '楼房分层'
  }
])

// 当步骤变化的时候，修改ui
watch(currentStep, (val) => {
  if (val === 0) {
    if (!buildingColumns.value.find(item => item.dataIndex === 'operation')) {
      buildingColumns.value.push({
        title: '操作',
        dataIndex: 'operation',
        align: 'center'
      })
    }
  }
  if (val === 1) {
    if (!buildingColumns.value.find(item => item.dataIndex === 'positionFix')) {
      buildingColumns.value.push({
        title: '定位',
        dataIndex: 'positionFix',
        align: 'center',
        width: '10%'
      })
    }
  }
  // 楼房分层，将后面两列删除
  if (val === 2) {
    buildingColumns.value = buildingColumns.value.filter(item => item.dataIndex !== 'positionFix' && item.dataIndex !== 'operation')
  }
})

const textDraw = computed(() => {
  let res = ''
  switch (currentStep.value) {
    case 0:
      res = '绘制图形'
      break;
    case 1:
      res = '户型裁剪'
      break;
    case 2:
      res = '楼层分层'
      break;
    default:
      break;
  }
  return res
})

const editableData = reactive({});
// 可编辑表格配置
const edit = key => {
    editableData[key] = cloneDeep(buildingData.value.filter(item => key === item.key)[0]);
};
const save = key => {
    Object.assign(buildingData.value.filter(item => key === item.key)[0], editableData[key]);
    delete editableData[key];
};
const cancel = key => {
    delete editableData[key];
};

// 回退到初始状态
const refresh=()=>{
  primitives.length && primitives.forEach(primitive=>{
    viewer.scene.primitives.remove(primitive)
  })
  primitives.length=0
  viewer.entities.removeAll()
  lastDrawEnt=null
  // 变量初始化
  currentStep.value=0
  floorNum.value=0
  minHeight.value=0
  maxHeight.value=0
  divideHeight.value=0
  updateActive.value=false
  showDivide.value=false
  buildingData.value=[]
  // 清除正在绘制的事件
  drawEntEvent && drawTool.DrawEndEvent.removeEventListener(drawEntEvent)
}

// 定位到区域
const focusOnArea=(key)=>{
  console.log(key);
  const targetEnt=viewer.entities.values.find(item=>item.name===key)
  if(targetEnt){
    // 闪烁实体
    binkEntityMaterial(targetEnt)
  }
}

// 下一步
const nextStep=()=>{
  // 只有当表格数据不为空的时候，才走下一步
  if(buildingData.value?.length){
    currentStep.value++
  }else{
    message.warn('请先绘制区域')
  }
}
let primitives=[]
// 楼房分层
const divideFloor=()=>{
  if(floorNum.value){
    // 从挑空层到顶层的每一层的高度
    const itemHeight=(maxHeight.value-divideHeight.value)/(floorNum.value-1)
    // 遍历所有的单元楼
    buildingData.value.forEach(data=>{
      const {position}=data
      console.log(position);
      // 首先将经纬度的字符串数据变为经纬度数组
      const positionArr=position.split(',').map(item=>Number(item))
      // 再将坐标变为笛卡尔坐标数组
      const positions=new Cesium.Cartesian3.fromDegreesArray(positionArr)
      // 再构造polygonHerchay
      const polygonHerachy=new Cesium.PolygonHierarchy(positions)
      // 底部高度
      let height
      // 拉伸高度
      let extrudedHeight
      // 从一楼到顶楼依次遍历，添加每一户的primitive
      for(let i=0;i<floorNum.value;i++){
        if(i===0){
          height=minHeight.value
          extrudedHeight=divideHeight.value
        }else{
          // 对于其他楼栋
          height=divideHeight.value+(i-1)*itemHeight
          extrudedHeight=divideHeight.value+(i)*itemHeight
        }
        let primitive=new Cesium.ClassificationPrimitive({
          geometryInstances:new Cesium.GeometryInstance({
            geometry:new Cesium.PolygonGeometry({
              polygonHierarchy:polygonHerachy,
              height,
              extrudedHeight
            }),
            attributes:{
              color:Cesium.ColorGeometryInstanceAttribute.fromColor(
                Cesium.Color.fromRandom({alpha:0.3})
              )
            }
          }),
          // 设置图元贴合在3dtile上
          classificationType:Cesium.ClassificationType.CESIUM_3D_TILE
        })
        primitives.push(primitive)
        viewer.scene.primitives.add(primitive)
        // 将提交数据的开关显示
        updateActive.value=true
      }
    })
  }
}

// 服务上传
const updateInfo=async ()=>{
  // 单元楼数据
  const unitArr=buildingData.value.map(item=>Number(item.unit))
  const heightArr=[minHeight.value,divideHeight.value,maxHeight.value]
  const name=buildingData.value[0].posPrefix
  postDataSource.floorNum=Number(floorNum.value)
  postDataSource.unitArr=unitArr
  postDataSource.heightArr=heightArr
  postDataSource.name=name
  const res=await addHouse(postDataSource)
  if(res.code===200){
    refresh()
    message.success('分户成功')
  }
}

// 绘制范围
// 定义绘制工具，并激活，将绘制之后的数据存储到表格数据源以及服务数据源
let drawEntEvent
let polygonGeo
// 上一个步骤中绘制的实体
let lastDrawEnt
const activeDraw=()=>{
  // 步骤一：确认楼栋的范围
  if(currentStep.value===0){
    drawTool.active(drawTool.DrawTypes.Polygon)
    drawEntEvent=(ent,positions)=>{
      lastDrawEnt=ent
      // 将返回的笛卡尔坐标数组，变为geojson，方便传入服务
      const {geojson,positionLng}=changeCartesin3ToGeoJson(positions,'polygon')
      if(geojson){
        polygonGeo=geojson
        // 填充表格数据
        buildingData.value[0]={
          position:positionLng.join(','),
          posPrefix:'xxx小区xxx楼栋',
          unit:'1'
        }
        // 填充传入服务端的数据源
        postDataSource.polygonJson=JSON.stringify(geojson)
        // 绘制完成之后，清除绘制事件
        drawTool.DrawEndEvent.removeEventListener(drawEntEvent)
      }
    }
    drawTool.DrawEndEvent.addEventListener(drawEntEvent)
  }else if(currentStep.value===1){
    // 步骤二：确认小区单元范围，调用polyline进行绘制
    drawTool.active(drawTool.DrawTypes.Polyline)
    // 核心思路：根据绘制的polyline对多边形进行切割，得到被切割之后的多边形数组
    drawEntEvent=(ent,positions)=>{
      // 绘制结束事件中，将上一步的实体清除，并将当前绘制的线段也清除
      lastDrawEnt && viewer.entities.remove(lastDrawEnt)
      lastDrawEnt=null
      drawTool.removeAllDrawEnts()
      // 将线段的笛卡尔坐标转为geojson数据格式
      const {geojson:linejson}=changeCartesin3ToGeoJson(positions,'polyline')
      // 得到被裁剪后的多边形数组
      const polygonCollection=polygonCut(polygonGeo,linejson)
      // 可能会有不合法的操作
      if(polygonCollection.features.length<=1){
        message.warn('请切割区域')
        return
      }
      if(polygonCollection){
        // 1.构造服务数据,这里传入的数据是Array<string>
        postDataSource.polygonJsonArr=polygonCollection.features.map(item=>{
          return JSON.stringify(item.geometry)
        })
        // 2.构造三维实体，贴到物体表面
        Cesium.GeoJsonDataSource.load(polygonCollection,{clampToGround:true}).then(info=>{
          // 表格数据
          const dataSource=[]
          info.entities.values.forEach((item,index)=>{
            item.polygon.material=Cesium.Color.fromRandom({alpha:0.5})
            item.name=index
            viewer.entities.add(item)
            // 添加表格数据
            // 坐标可以从geojson数据中获取，前缀使用步骤一中定义好的前缀
            dataSource.push({
				      key: index,
				      position: polygonCollection.features[index].geometry.coordinates.toString(),
				      posPrefix: buildingData.value[0].posPrefix,
				      unit: index + 1
			      })
          })
          buildingData.value=dataSource
          // 绘制完成之后，清除绘制事件
          drawTool.DrawEndEvent.removeEventListener(drawEntEvent)
        })
      }
    }
    drawTool.DrawEndEvent.addEventListener(drawEntEvent)
  }else if(currentStep.value===2){
    drawTool.active(drawTool.DrawTypes.Point)
    drawEntEvent=(ent,positions)=>{
      if(positions.length===3){
        // 绘制结束事件中，将上一步的实体清除，并将当前绘制的线段也清除
        viewer.entities.removeAll()
        // 依次得到三个点位置的高度
        const positionHeight=positions.map(position=>{
          const positionLng=cartesian3ToDegreesHeight(position)
          return positionLng[2]
        }).sort((a,b)=>a-b)
        minHeight.value=positionHeight[0]
        divideHeight.value=positionHeight[1]
        maxHeight.value=positionHeight[2]
        console.log(positionHeight);
        // 将隐藏的按钮打开
        showDivide.value=true
      }else{
        message.warn('请绘制底部，挑空，顶部三个位置的高度')
      }
    }
    drawTool.DrawEndEvent.addEventListener(drawEntEvent)
  }
}
</script>

<style scoped>
.button-area {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.draw-area {
  margin: 20px 0;
}

.draw-area span {
  margin: 0 4px;
}

.icon-draw {
  padding: 4px;
}

.icon-draw:hover {
  cursor: pointer;
  background-color: skyblue;
  color: #fff;
}

.floor-area span {
  margin: 0 10px;
}
</style>