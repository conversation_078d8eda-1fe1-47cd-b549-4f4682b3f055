<template>
  <div
    class="potree_container"
    style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px"
  >
    <div id="potree_render_area"></div>
    <div id="potree_sidebar_container"></div>
  </div>
</template>
<script setup>
import { onMounted } from 'vue'
import * as THREE from 'three'
const showNextSibling = (id) => {
  var element = document.querySelector(`#${id}`)
  if (element && element.nextElementSibling) {
    element.nextElementSibling.style.display = 'block'
  }
}
const initPotree = () => {
  window.viewer = new Potree.Viewer(
    document.getElementById('potree_render_area')
  )
  viewer.setEDLEnabled(true)
  viewer.setFOV(60)
  viewer.setPointBudget(1_000_000)
  viewer.loadSettingsFromURL()
  viewer.setDescription('某地块数据')

  viewer.loadGUI(() => {
  showNextSibling('menu_appearance')
  showNextSibling('menu_tools')
  showNextSibling('menu_scene')
  viewer.toggleSidebar()
  })

  // 加载并添加点云到场景中
  Potree.loadPointCloud(
    'https://scriptecho.cn/mschuetz/potree/resources/pointclouds/pix4d/eclepens/cloud.js',
    'web',
    (e) => {
      let scene = viewer.scene
      let pointcloud = e.pointcloud
      // scene.addPointCloud(pointcloud)

      let material = pointcloud.material
      material.size = 1
      material.pointSizeType = Potree.PointSizeType.ADAPTIVE
      material.shape = Potree.PointShape.SQUARE

      scene.addPointCloud(pointcloud)

      pointcloud.position.x = 0

      scene.view.position.set(900.06, -492.0, 345.79)
      scene.view.lookAt(new THREE.Vector3(566.49, -56.16, -47.9))
    }
  )
}

onMounted(async () => {
  initPotree()
})
</script>
<style scoped>
#app {
  width: 100%;
  height: 100vh;
}
</style>
