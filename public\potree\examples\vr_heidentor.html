<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
		<input id="tiffTest" type="button" value="tiff test" />
	</div>

	
	
	<script type="module">

		import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(90);
		viewer.setPointBudget(1_000_000);
		viewer.setMinNodeSize(0);
		viewer.loadSettingsFromURL();
		viewer.setBackground("skybox");
		viewer.setShowBoundingBox(false);
		
		viewer.setDescription(`Heidentor point cloud courtesy of <a href="http://archpro.lbg.ac.at/" target="_blank">Ludwig Boltzmann Institute - Archeological Prospection and Virtual Archeology</a> (26M points)`);
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			$("#menu_tools").next().show();
			$("#menu_scene").next().show();
			//viewer.toggleSidebar();
		});
		
		Potree.loadPointCloud("https://potree.org/pointclouds/heidentor/metadata.json", "Heidentor", function(e){
			viewer.scene.addPointCloud(e.pointcloud);
			e.pointcloud.position.z = 0;
			let material = e.pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			
			viewer.scene.view.setView(
				[4.681, 3.228, 2.074],
				[3.681, 3.222, 2.359],
			);

		});


		{ // ground plane
			const texture = new THREE.TextureLoader().load(`${Potree.resourcePath}/textures/rectangle.png`);

			texture.wrapS = THREE.RepeatWrapping;
			texture.wrapT = THREE.RepeatWrapping;
			texture.repeat.x = 10;
			texture.repeat.y = 10;
			texture.anisotropy = 16;

			let geometry = new THREE.PlaneBufferGeometry(1, 1, 10, 10);
			let material = new THREE.MeshBasicMaterial();
			material.map = texture;
			material.needsUpdate = true;
			let plane = new THREE.Mesh(geometry, material);
			plane.scale.set(18, 18, 18);
			plane.position.set(-2, 3, 0);
			plane.material.color.setRGB(0.8, 0.8, 0.8);
			window.plane = plane;
			viewer.scene.scene.add(plane);
		}


	</script>
	
  </body>
</html>
