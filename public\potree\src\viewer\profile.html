<div id="profile_window" style="
	position: absolute; 
	width: 84%;
	left: 15%; top: 55%; height: 44%;
	margin: 5px;
	border: 1px solid black; 
	display: none; box-sizing: border-box; z-index:10000">
	
	<div id="profile_titlebar" class="pv-titlebar" style="display: flex; position: absolute; height: 30px; width: 100%; box-sizing: border-box;">
		<span style="padding-right: 10px">
			<span id="profile_window_title" data-i18n="profile.title"></span>
		</span>
		<span id="profileInfo" style="flex-grow: 1; flex-direction: row"> </span>
		<!-- <span id="profile_toggle_size_button" class="ui-icon ui-icon-newwin profile-button"> </span> -->
		<!--<span id="closeProfileContainer" class="ui-icon ui-icon-close profile-button"> </span>-->
		<img id="closeProfileContainer" class="button-icon" style="width: 24px; height: 24px; margin: 4px"/>
	</div>
	
	<div style="position: absolute; top: 30px; width: 100%; height: calc(100% - 30px); box-sizing: border-box;" class="pw_content">
		
		<span class="pv-main-color" style="height: 100%; width: 100%; padding: 5px; display:flex; flex-direction: column; box-sizing: border-box;">
			<div style=" width: 100%; color: #9d9d9d; margin: 5px; display: flex; flex-direction: row; box-sizing: border-box;">
				<span data-i18n="profile.nb_points"></span>: &nbsp;
				<span id="profile_num_points">-</span>
				<!--<span id="profile_threshold" style="width: 300px">
					Threshold: <span id="potree_profile_threshold_label">123</span> <div id="potree_profile_threshold_slider"></div>
				</span>-->
				<span style="flex-grow: 1;"></span>
				<span>
				
					<!-- <span contenteditable="true" style="display: inline-block; 
						width: 24px; height: 24px; 
						vertical-align: top; 
						background: white; color:black"></span> -->

					<input id="potree_profile_rotate_amount" 
						type="text" maxlength="4" value="10" style="
						display: inline-block; 
						width: 2.5em; 
						vertical-align: top; 
						background: white;
						margin: 2px;
						"></span>
					
					<img id="potree_profile_rotate_cw" class="text-icon"/>
					<img id="potree_profile_rotate_ccw" class="text-icon"/>

					<img id="potree_profile_move_forward" class="text-icon"/>
					<img id="potree_profile_move_backward" class="text-icon"/>

					<a id="potree_download_profile_dxf2D_link" href="#" download="profile_2D.dxf">
						<img id="potree_download_dxf2D_icon" class="text-icon"/>
					</a>

					<a id="potree_download_profile_dxf3D_link" href="#" download="profile_3D.dxf">
						<img id="potree_download_dxf3D_icon" class="text-icon"/>
					</a>

					<a id="potree_download_profile_ortho_link" href="#" download="profile.csv">
						<img id="potree_download_csv_icon" class="text-icon"/>
					</a>
					
					<a id="potree_download_profile_link" href="#" download="profile.las">
						<img id="potree_download_las_icon" class="text-icon"/>
					</a>
					
				</span>
			</div>
			
			<div id="profile_draw_container" style="
				width: 100%; 
				flex-grow: 1; 
				position: relative; height: 100%; 
				box-sizing: border-box; user-select: none">
				<div style="
					position: absolute; 
					left: 41px; 
					top: 0; 
					bottom: 20; 
					width: calc(100% - 41px); 
					height: calc(100% - 20px); 
					background-color: #000000;
					"></div>
				<svg id="profileSVG" style="
					fill: #9d9d9d;
					position: absolute; 
					left: 0; right: 0; 
					top: 0; bottom: 0; 
					width: 100%; 
					height: 100%;
					"></svg>
				<div id="profileCanvasContainer" style="
					position: absolute; 
					left: 41px; 
					top: 0; 
					bottom: 20; 
					width: calc(100% - 41px); 
					height: calc(100% - 20px); 
					/*background-color: #000000;*/
					"></div>
				
				<div id="profileSelectionProperties" style="
					position: absolute; 
					left: 50px; 
					top: 10px; 
					background-color: black;
					color: white;
					opacity: 0.7;
					padding: 5px;
					border: 1px solid white;
					user-select: text;
					">
					position: asdsadf asdf<br>
					rgb: 123 423 123
					</div>
				
			</div>
		</span>
	</div>

</div>